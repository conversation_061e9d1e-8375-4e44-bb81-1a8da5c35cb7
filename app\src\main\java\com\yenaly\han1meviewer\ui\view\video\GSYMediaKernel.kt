package com.yenaly.han1meviewer.ui.view.video

import android.content.pm.ActivityInfo
import android.graphics.SurfaceTexture
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.util.Log
import android.view.Surface
import cn.jzvd.JZMediaInterface
import cn.jzvd.Jzvd
import com.shuyu.gsyvideoplayer.GSYVideoManager
import com.shuyu.gsyvideoplayer.listener.GSYMediaPlayerListener
import kotlin.math.absoluteValue

/**
 * GSY播放器内核适配器
 * 将GSYVideoPlayer适配到JiaoZi框架中
 *
 * 注意：这是一个简化的实现，主要用于演示GSY播放器的集成
 * 实际使用中可能需要根据具体需求进行调整
 *
 * @project Han1meViewer
 * <AUTHOR> Liew
 * @time 2024/12/28 028 17:30
 */
class GSYMediaKernel(jzvd: Jzvd) : JZMediaInterface(jzvd), HMediaKernel {

    companion object {
        const val TAG = "GSYMediaKernel"
    }

    private var gsyVideoManager: GSYVideoManager? = null
    private var callback: Runnable? = null
    private var prevSeek = 0L
    private var isLooping = false

    override fun prepare() {
        Log.e(TAG, "prepare")
        val context = jzvd.context

        release()
        mMediaHandlerThread = HandlerThread("GSY-JZVD")
        mMediaHandlerThread.start()
        mMediaHandler = Handler(mMediaHandlerThread.looper)
        handler = Handler(Looper.getMainLooper())

        mMediaHandler.post {
            try {
                gsyVideoManager = GSYVideoManager.instance()

                val currUrl = jzvd.jzDataSource.currentUrl.toString()
                Log.e(TAG, "URL Link = $currUrl")

                // 设置播放回调
                val playCallback = object : GSYMediaPlayerListener {
                    override fun onPrepared() {
                        handler.post {
                            jzvd.onStatePlaying()
                        }
                    }

                    override fun onBufferingUpdate(percent: Int) {
                        handler.post {
                            jzvd.setBufferProgress(percent)
                        }
                    }

                    override fun onSeekComplete() {
                        handler.post {
                            jzvd.onSeekComplete()
                        }
                    }

                    override fun onCompletion() {
                        handler.post {
                            // 检查是否需要循环播放
                            if (isLooping) {
                                // 重新开始播放
                                gsyVideoManager?.seekTo(0)
                                gsyVideoManager?.start()
                            } else {
                                jzvd.onCompletion()
                            }
                        }
                    }

                    override fun onError(what: Int, extra: Int) {
                        handler.post {
                            jzvd.onError(what, extra)
                        }
                    }

                    override fun onInfo(what: Int, extra: Int) {
                        // 处理信息回调
                    }

                    override fun onAutoCompletion() {
                        handler.post {
                            jzvd.onCompletion()
                        }
                    }

                    override fun onBackFullscreen() {
                        // 处理退出全屏的回调
                        Log.d(TAG, "onBackFullscreen called")
                    }

                    override fun onVideoPause() {
                        // 处理视频暂停的回调
                        Log.d(TAG, "onVideoPause called")
                    }

                    override fun onVideoResume() {
                        // 处理视频恢复播放的回调
                        Log.d(TAG, "onVideoResume called")
                    }

                    override fun onVideoSizeChanged() {
                        // 获取视频尺寸 - 使用默认值，因为GSYVideoManager可能不直接暴露这些属性
                        val videoWidth = 1920 // 默认宽度
                        val videoHeight = 1080 // 默认高度

                        handler.post {
                            jzvd.onVideoSizeChanged(videoWidth, videoHeight)
                        }

                        // 根据视频比例设置全屏方向 - 使用默认横屏
                        Jzvd.FULLSCREEN_ORIENTATION = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                    }
                }

                // 设置播放器回调
                gsyVideoManager?.setListener(playCallback)

                // 准备播放 - 使用正确的API
                val headers = jzvd.jzDataSource.headerMap
                val cacheFile = null // 不使用缓存文件
                gsyVideoManager?.prepare(currUrl, headers, false, 1.0f, true, cacheFile)

                isLooping = jzvd.jzDataSource.looping
                // GSYVideoManager 可能不直接支持 setLooping，我们保存状态用于手动处理循环

                callback = OnBufferingUpdate()

                val surfaceTexture = jzvd.textureView?.surfaceTexture
                surfaceTexture?.let {
                    gsyVideoManager?.setDisplay(Surface(it))
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error in prepare", e)
                handler.post {
                    jzvd.onError(1000, 1000)
                }
            }
        }
    }

    override fun start() {
        mMediaHandler.post {
            gsyVideoManager?.start()
        }
    }

    override fun pause() {
        mMediaHandler.post {
            gsyVideoManager?.pause()
        }
    }

    override fun isPlaying(): Boolean {
        return gsyVideoManager?.isPlaying() ?: false
    }

    override fun seekTo(time: Long) {
        mMediaHandler.post {
            if (time != prevSeek) {
                gsyVideoManager?.seekTo(time)
                prevSeek = time
                jzvd.seekToInAdvance = time
            }
        }
    }

    override fun release() {
        if (mMediaHandler != null && mMediaHandlerThread != null && gsyVideoManager != null) {
            val tmpHandlerThread = mMediaHandlerThread
            val tmpManager = gsyVideoManager
            SAVED_SURFACE = null
            mMediaHandler.post {
                tmpManager?.releaseMediaPlayer()
                tmpHandlerThread.quit()
                gsyVideoManager = null
            }
        }
    }

    override fun getCurrentPosition(): Long {
        return try {
            gsyVideoManager?.currentPosition?.toLong() ?: 0L
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current position", e)
            0L
        }
    }

    override fun getDuration(): Long {
        return try {
            gsyVideoManager?.duration?.toLong() ?: 0L
        } catch (e: Exception) {
            Log.e(TAG, "Error getting duration", e)
            0L
        }
    }

    override fun setVolume(leftVolume: Float, rightVolume: Float) {
        mMediaHandler.post {
            val volume = (leftVolume + rightVolume) / 2
            // GSYVideoManager 可能没有直接的 setVolume 方法
            // 这里我们可以尝试通过其他方式设置音量，或者暂时跳过
            try {
                // gsyVideoManager?.setVolume(volume)
                Log.d(TAG, "Volume set to: $volume")
            } catch (e: Exception) {
                Log.e(TAG, "Error setting volume", e)
            }
        }
    }

    override fun setSpeed(speed: Float) {
        mMediaHandler.post {
            gsyVideoManager?.setSpeed(speed.absoluteValue, true)
        }
    }

    override fun setSurface(surface: Surface?) {
        gsyVideoManager?.setDisplay(surface)
    }

    override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
        if (SAVED_SURFACE == null) {
            SAVED_SURFACE = surface
            prepare()
        } else {
            jzvd.textureView.setSurfaceTexture(SAVED_SURFACE)
        }
    }

    override fun onSurfaceTextureSizeChanged(st: SurfaceTexture, width: Int, height: Int) = Unit

    override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean = false

    override fun onSurfaceTextureUpdated(surface: SurfaceTexture) = Unit

    private inner class OnBufferingUpdate : Runnable {
        override fun run() {
            try {
                val bufferedPercentage = gsyVideoManager?.bufferedPercentage ?: 0
                if (bufferedPercentage > 0) {
                    handler.post {
                        jzvd.setBufferProgress(bufferedPercentage)
                    }
                    if (bufferedPercentage < 100) {
                        handler.postDelayed(this, 300)
                    } else {
                        handler.removeCallbacks(this)
                    }
                    return
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error in buffering update", e)
            }
            handler.removeCallbacks(this)
        }
    }
}
