package com.yenaly.han1meviewer.logic.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.yenaly.han1meviewer.logic.entity.HKeyframeEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class HKeyframeDao_Impl extends HKeyframeDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<HKeyframeEntity> __insertionAdapterOfHKeyframeEntity;

  private final HKeyframeEntity.KeyframeTypeConverter __keyframeTypeConverter = new HKeyframeEntity.KeyframeTypeConverter();

  private final EntityDeletionOrUpdateAdapter<HKeyframeEntity> __deletionAdapterOfHKeyframeEntity;

  private final EntityDeletionOrUpdateAdapter<HKeyframeEntity> __updateAdapterOfHKeyframeEntity;

  public HKeyframeDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfHKeyframeEntity = new EntityInsertionAdapter<HKeyframeEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `HKeyframeEntity` (`videoCode`,`title`,`keyframes`,`lastModifiedTime`,`createdTime`,`author`) VALUES (?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HKeyframeEntity entity) {
        statement.bindString(1, entity.getVideoCode());
        statement.bindString(2, entity.getTitle());
        final String _tmp = __keyframeTypeConverter.fromKeyframeList(entity.getKeyframes());
        statement.bindString(3, _tmp);
        statement.bindLong(4, entity.getLastModifiedTime());
        statement.bindLong(5, entity.getCreatedTime());
        if (entity.getAuthor() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getAuthor());
        }
      }
    };
    this.__deletionAdapterOfHKeyframeEntity = new EntityDeletionOrUpdateAdapter<HKeyframeEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `HKeyframeEntity` WHERE `videoCode` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HKeyframeEntity entity) {
        statement.bindString(1, entity.getVideoCode());
      }
    };
    this.__updateAdapterOfHKeyframeEntity = new EntityDeletionOrUpdateAdapter<HKeyframeEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR REPLACE `HKeyframeEntity` SET `videoCode` = ?,`title` = ?,`keyframes` = ?,`lastModifiedTime` = ?,`createdTime` = ?,`author` = ? WHERE `videoCode` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HKeyframeEntity entity) {
        statement.bindString(1, entity.getVideoCode());
        statement.bindString(2, entity.getTitle());
        final String _tmp = __keyframeTypeConverter.fromKeyframeList(entity.getKeyframes());
        statement.bindString(3, _tmp);
        statement.bindLong(4, entity.getLastModifiedTime());
        statement.bindLong(5, entity.getCreatedTime());
        if (entity.getAuthor() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getAuthor());
        }
        statement.bindString(7, entity.getVideoCode());
      }
    };
  }

  @Override
  public Object insert(final HKeyframeEntity entity, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfHKeyframeEntity.insert(entity);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object delete(final HKeyframeEntity entity, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfHKeyframeEntity.handle(entity);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object update(final HKeyframeEntity entity, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfHKeyframeEntity.handle(entity);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<HKeyframeEntity>> loadAll() {
    final String _sql = "SELECT * FROM HKeyframeEntity ORDER BY createdTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"HKeyframeEntity"}, new Callable<List<HKeyframeEntity>>() {
      @Override
      @NonNull
      public List<HKeyframeEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCode");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfKeyframes = CursorUtil.getColumnIndexOrThrow(_cursor, "keyframes");
          final int _cursorIndexOfLastModifiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModifiedTime");
          final int _cursorIndexOfCreatedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createdTime");
          final int _cursorIndexOfAuthor = CursorUtil.getColumnIndexOrThrow(_cursor, "author");
          final List<HKeyframeEntity> _result = new ArrayList<HKeyframeEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HKeyframeEntity _item;
            final String _tmpVideoCode;
            _tmpVideoCode = _cursor.getString(_cursorIndexOfVideoCode);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final List<HKeyframeEntity.Keyframe> _tmpKeyframes;
            final String _tmp;
            _tmp = _cursor.getString(_cursorIndexOfKeyframes);
            _tmpKeyframes = __keyframeTypeConverter.toKeyframeList(_tmp);
            final long _tmpLastModifiedTime;
            _tmpLastModifiedTime = _cursor.getLong(_cursorIndexOfLastModifiedTime);
            final long _tmpCreatedTime;
            _tmpCreatedTime = _cursor.getLong(_cursorIndexOfCreatedTime);
            final String _tmpAuthor;
            if (_cursor.isNull(_cursorIndexOfAuthor)) {
              _tmpAuthor = null;
            } else {
              _tmpAuthor = _cursor.getString(_cursorIndexOfAuthor);
            }
            _item = new HKeyframeEntity(_tmpVideoCode,_tmpTitle,_tmpKeyframes,_tmpLastModifiedTime,_tmpCreatedTime,_tmpAuthor);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<HKeyframeEntity>> loadAll(final String keyword) {
    final String _sql = "SELECT * FROM HKeyframeEntity WHERE `title` LIKE '%' || ? || '%' OR `videoCode` == ? ORDER BY createdTime DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, keyword);
    _argIndex = 2;
    _statement.bindString(_argIndex, keyword);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"HKeyframeEntity"}, new Callable<List<HKeyframeEntity>>() {
      @Override
      @NonNull
      public List<HKeyframeEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCode");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfKeyframes = CursorUtil.getColumnIndexOrThrow(_cursor, "keyframes");
          final int _cursorIndexOfLastModifiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModifiedTime");
          final int _cursorIndexOfCreatedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createdTime");
          final int _cursorIndexOfAuthor = CursorUtil.getColumnIndexOrThrow(_cursor, "author");
          final List<HKeyframeEntity> _result = new ArrayList<HKeyframeEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HKeyframeEntity _item;
            final String _tmpVideoCode;
            _tmpVideoCode = _cursor.getString(_cursorIndexOfVideoCode);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final List<HKeyframeEntity.Keyframe> _tmpKeyframes;
            final String _tmp;
            _tmp = _cursor.getString(_cursorIndexOfKeyframes);
            _tmpKeyframes = __keyframeTypeConverter.toKeyframeList(_tmp);
            final long _tmpLastModifiedTime;
            _tmpLastModifiedTime = _cursor.getLong(_cursorIndexOfLastModifiedTime);
            final long _tmpCreatedTime;
            _tmpCreatedTime = _cursor.getLong(_cursorIndexOfCreatedTime);
            final String _tmpAuthor;
            if (_cursor.isNull(_cursorIndexOfAuthor)) {
              _tmpAuthor = null;
            } else {
              _tmpAuthor = _cursor.getString(_cursorIndexOfAuthor);
            }
            _item = new HKeyframeEntity(_tmpVideoCode,_tmpTitle,_tmpKeyframes,_tmpLastModifiedTime,_tmpCreatedTime,_tmpAuthor);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object findBy(final String videoCode,
      final Continuation<? super HKeyframeEntity> $completion) {
    final String _sql = "SELECT * FROM HKeyframeEntity WHERE `videoCode` == ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, videoCode);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<HKeyframeEntity>() {
      @Override
      @Nullable
      public HKeyframeEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCode");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfKeyframes = CursorUtil.getColumnIndexOrThrow(_cursor, "keyframes");
          final int _cursorIndexOfLastModifiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModifiedTime");
          final int _cursorIndexOfCreatedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createdTime");
          final int _cursorIndexOfAuthor = CursorUtil.getColumnIndexOrThrow(_cursor, "author");
          final HKeyframeEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpVideoCode;
            _tmpVideoCode = _cursor.getString(_cursorIndexOfVideoCode);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final List<HKeyframeEntity.Keyframe> _tmpKeyframes;
            final String _tmp;
            _tmp = _cursor.getString(_cursorIndexOfKeyframes);
            _tmpKeyframes = __keyframeTypeConverter.toKeyframeList(_tmp);
            final long _tmpLastModifiedTime;
            _tmpLastModifiedTime = _cursor.getLong(_cursorIndexOfLastModifiedTime);
            final long _tmpCreatedTime;
            _tmpCreatedTime = _cursor.getLong(_cursorIndexOfCreatedTime);
            final String _tmpAuthor;
            if (_cursor.isNull(_cursorIndexOfAuthor)) {
              _tmpAuthor = null;
            } else {
              _tmpAuthor = _cursor.getString(_cursorIndexOfAuthor);
            }
            _result = new HKeyframeEntity(_tmpVideoCode,_tmpTitle,_tmpKeyframes,_tmpLastModifiedTime,_tmpCreatedTime,_tmpAuthor);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Flow<HKeyframeEntity> observe(final String videoCode) {
    final String _sql = "SELECT * FROM HKeyframeEntity WHERE `videoCode` == ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindString(_argIndex, videoCode);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"HKeyframeEntity"}, new Callable<HKeyframeEntity>() {
      @Override
      @Nullable
      public HKeyframeEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfVideoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCode");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfKeyframes = CursorUtil.getColumnIndexOrThrow(_cursor, "keyframes");
          final int _cursorIndexOfLastModifiedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastModifiedTime");
          final int _cursorIndexOfCreatedTime = CursorUtil.getColumnIndexOrThrow(_cursor, "createdTime");
          final int _cursorIndexOfAuthor = CursorUtil.getColumnIndexOrThrow(_cursor, "author");
          final HKeyframeEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpVideoCode;
            _tmpVideoCode = _cursor.getString(_cursorIndexOfVideoCode);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final List<HKeyframeEntity.Keyframe> _tmpKeyframes;
            final String _tmp;
            _tmp = _cursor.getString(_cursorIndexOfKeyframes);
            _tmpKeyframes = __keyframeTypeConverter.toKeyframeList(_tmp);
            final long _tmpLastModifiedTime;
            _tmpLastModifiedTime = _cursor.getLong(_cursorIndexOfLastModifiedTime);
            final long _tmpCreatedTime;
            _tmpCreatedTime = _cursor.getLong(_cursorIndexOfCreatedTime);
            final String _tmpAuthor;
            if (_cursor.isNull(_cursorIndexOfAuthor)) {
              _tmpAuthor = null;
            } else {
              _tmpAuthor = _cursor.getString(_cursorIndexOfAuthor);
            }
            _result = new HKeyframeEntity(_tmpVideoCode,_tmpTitle,_tmpKeyframes,_tmpLastModifiedTime,_tmpCreatedTime,_tmpAuthor);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
