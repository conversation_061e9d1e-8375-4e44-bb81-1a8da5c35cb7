# GSYVideoPlayer 集成文档

## 概述

本项目已成功集成 GSYVideoPlayer 作为新的视频播放器选项。用户现在可以在设置中选择使用 GSYVideoPlayer 作为播放内核。

**注意**: 当前实现是一个基础版本，主要用于演示GSY播放器的集成。实际使用中可能需要根据具体的GSYVideoPlayer API进行进一步调整和优化。

## 实现的功能

### 1. 新增播放器内核
- 在 `HMediaKernel.Type` 枚举中添加了 `GSYPlayer` 选项
- 创建了 `GSYMediaKernel` 类来适配 GSYVideoPlayer 到现有的 JiaoZi 框架

### 2. 设置页面支持
- 播放器设置页面会自动显示新的 GSYPlayer 选项
- 用户可以在 "切换播放器内核" 设置中选择 GSYPlayer

### 3. 功能特性
- 支持多种视频格式（MP4, M3U8等）
- 支持自定义播放速度
- 支持手势控制（音量、亮度、进度）
- 支持全屏播放
- 支持循环播放
- 支持自定义请求头

## 文件结构

```
app/src/main/java/com/yenaly/han1meviewer/ui/view/video/
├── HMediaKernel.kt          # 播放器内核枚举（已更新）
├── GSYMediaKernel.kt        # GSY播放器适配器（新增）
├── HJzvdStd.kt             # 原有播放器UI（保持不变）
└── HanimeDataSource.kt     # 数据源（保持不变）

gradle/libs.versions.toml    # 依赖版本管理（已更新）
app/build.gradle.kts         # 构建配置（已更新）
```

## 依赖更新

### gradle/libs.versions.toml
```toml
gsyVideoPlayer = "10.0.0"

gsy-video-player = { group = "com.github.CarGuo.GSYVideoPlayer", name = "gsyvideoplayer", version.ref = "gsyVideoPlayer" }
```

### app/build.gradle.kts
```kotlin
implementation(libs.gsy.video.player)
```

## 使用方法

### 1. 用户操作
1. 打开应用设置
2. 进入 "播放器设定"
3. 选择 "切换播放器内核"
4. 选择 "GSYPlayer"
5. 重启应用或重新播放视频

### 2. 开发者配置
播放器内核的选择是自动的，基于用户在设置中的选择：

```kotlin
val kernel = HMediaKernel.Type.fromString(Preferences.switchPlayerKernel)
binding.videoPlayer.setUp(dataSource, Jzvd.SCREEN_NORMAL, kernel)
```

## 技术实现

### GSYMediaKernel 类
- 继承自 `JZMediaInterface` 和 `HMediaKernel`
- 使用 `GSYVideoManager` 作为底层播放引擎
- 实现了所有必要的播放控制方法
- 提供了与 JiaoZi 框架的完整兼容性

### 主要方法
- `prepare()`: 初始化播放器并准备播放
- `start()`: 开始播放
- `pause()`: 暂停播放
- `seekTo()`: 跳转到指定位置
- `setSpeed()`: 设置播放速度
- `release()`: 释放播放器资源

## 优势

### GSYVideoPlayer 的优势
1. **更好的性能**: 基于 ExoPlayer 和 IJKPlayer，性能更优
2. **更多格式支持**: 支持更多视频格式和编解码器
3. **更好的兼容性**: 在各种设备上有更好的兼容性
4. **活跃维护**: 项目维护活跃，bug修复及时
5. **丰富功能**: 支持更多高级功能

### 与现有架构的兼容性
- 完全兼容现有的 JiaoZi 播放器框架
- 不影响现有的播放器功能
- 用户可以随时切换回原有的播放器内核

## 测试建议

1. **基本播放测试**
   - 测试各种视频格式的播放
   - 测试播放控制（播放、暂停、跳转）
   - 测试播放速度调节

2. **UI交互测试**
   - 测试手势控制
   - 测试全屏切换
   - 测试设置页面的播放器切换

3. **兼容性测试**
   - 测试不同设备的兼容性
   - 测试不同Android版本的兼容性
   - 测试内存使用情况

## 注意事项

1. **首次使用**: 用户首次选择 GSYPlayer 后建议重启应用
2. **性能监控**: 建议监控内存使用和播放性能
3. **错误处理**: GSYMediaKernel 包含了完整的错误处理机制
4. **资源释放**: 播放器会在适当的时候自动释放资源

## 未来改进

1. 可以考虑添加更多 GSYVideoPlayer 特有的功能
2. 可以优化播放器切换的用户体验
3. 可以添加播放器性能监控和统计
4. 可以考虑支持更多播放器内核选项
