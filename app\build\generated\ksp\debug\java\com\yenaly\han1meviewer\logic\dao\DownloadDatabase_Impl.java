package com.yenaly.han1meviewer.logic.dao;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class DownloadDatabase_Impl extends DownloadDatabase {
  private volatile HanimeDownloadDao _hanimeDownloadDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(2) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `HanimeDownloadEntity` (`coverUrl` TEXT NOT NULL, `title` TEXT NOT NULL, `addDate` INTEGER NOT NULL, `videoCode` TEXT NOT NULL, `videoUri` TEXT NOT NULL, `quality` TEXT NOT NULL, `videoUrl` TEXT NOT NULL, `length` INTEGER NOT NULL, `downloadedLength` INTEGER NOT NULL, `isDownloading` INTEGER NOT NULL, `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '0893e1bac5d1da11f2c609f4edbe64de')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `HanimeDownloadEntity`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsHanimeDownloadEntity = new HashMap<String, TableInfo.Column>(11);
        _columnsHanimeDownloadEntity.put("coverUrl", new TableInfo.Column("coverUrl", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("addDate", new TableInfo.Column("addDate", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("videoCode", new TableInfo.Column("videoCode", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("videoUri", new TableInfo.Column("videoUri", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("quality", new TableInfo.Column("quality", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("videoUrl", new TableInfo.Column("videoUrl", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("length", new TableInfo.Column("length", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("downloadedLength", new TableInfo.Column("downloadedLength", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("isDownloading", new TableInfo.Column("isDownloading", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsHanimeDownloadEntity.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysHanimeDownloadEntity = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesHanimeDownloadEntity = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoHanimeDownloadEntity = new TableInfo("HanimeDownloadEntity", _columnsHanimeDownloadEntity, _foreignKeysHanimeDownloadEntity, _indicesHanimeDownloadEntity);
        final TableInfo _existingHanimeDownloadEntity = TableInfo.read(db, "HanimeDownloadEntity");
        if (!_infoHanimeDownloadEntity.equals(_existingHanimeDownloadEntity)) {
          return new RoomOpenHelper.ValidationResult(false, "HanimeDownloadEntity(com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity).\n"
                  + " Expected:\n" + _infoHanimeDownloadEntity + "\n"
                  + " Found:\n" + _existingHanimeDownloadEntity);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "0893e1bac5d1da11f2c609f4edbe64de", "b3af652dd8646e7227ab697cbc0e1556");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "HanimeDownloadEntity");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `HanimeDownloadEntity`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(HanimeDownloadDao.class, HanimeDownloadDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public HanimeDownloadDao getHanimeDownloadDao() {
    if (_hanimeDownloadDao != null) {
      return _hanimeDownloadDao;
    } else {
      synchronized(this) {
        if(_hanimeDownloadDao == null) {
          _hanimeDownloadDao = new HanimeDownloadDao_Impl(this);
        }
        return _hanimeDownloadDao;
      }
    }
  }
}
