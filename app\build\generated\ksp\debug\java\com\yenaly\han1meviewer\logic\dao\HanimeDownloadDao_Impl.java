package com.yenaly.han1meviewer.logic.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomDatabaseKt;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity;
import java.lang.Boolean;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class HanimeDownloadDao_Impl extends HanimeDownloadDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<HanimeDownloadEntity> __insertionAdapterOfHanimeDownloadEntity;

  private final EntityDeletionOrUpdateAdapter<HanimeDownloadEntity> __deletionAdapterOfHanimeDownloadEntity;

  private final EntityDeletionOrUpdateAdapter<HanimeDownloadEntity> __updateAdapterOfHanimeDownloadEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteBy;

  private final SharedSQLiteStatement __preparedStmtOfPauseAll;

  public HanimeDownloadDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfHanimeDownloadEntity = new EntityInsertionAdapter<HanimeDownloadEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `HanimeDownloadEntity` (`coverUrl`,`title`,`addDate`,`videoCode`,`videoUri`,`quality`,`videoUrl`,`length`,`downloadedLength`,`isDownloading`,`id`) VALUES (?,?,?,?,?,?,?,?,?,?,nullif(?, 0))";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HanimeDownloadEntity entity) {
        statement.bindString(1, entity.getCoverUrl());
        statement.bindString(2, entity.getTitle());
        statement.bindLong(3, entity.getAddDate());
        statement.bindString(4, entity.getVideoCode());
        statement.bindString(5, entity.getVideoUri());
        statement.bindString(6, entity.getQuality());
        statement.bindString(7, entity.getVideoUrl());
        statement.bindLong(8, entity.getLength());
        statement.bindLong(9, entity.getDownloadedLength());
        final int _tmp = entity.isDownloading() ? 1 : 0;
        statement.bindLong(10, _tmp);
        statement.bindLong(11, entity.getId());
      }
    };
    this.__deletionAdapterOfHanimeDownloadEntity = new EntityDeletionOrUpdateAdapter<HanimeDownloadEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `HanimeDownloadEntity` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HanimeDownloadEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfHanimeDownloadEntity = new EntityDeletionOrUpdateAdapter<HanimeDownloadEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR REPLACE `HanimeDownloadEntity` SET `coverUrl` = ?,`title` = ?,`addDate` = ?,`videoCode` = ?,`videoUri` = ?,`quality` = ?,`videoUrl` = ?,`length` = ?,`downloadedLength` = ?,`isDownloading` = ?,`id` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final HanimeDownloadEntity entity) {
        statement.bindString(1, entity.getCoverUrl());
        statement.bindString(2, entity.getTitle());
        statement.bindLong(3, entity.getAddDate());
        statement.bindString(4, entity.getVideoCode());
        statement.bindString(5, entity.getVideoUri());
        statement.bindString(6, entity.getQuality());
        statement.bindString(7, entity.getVideoUrl());
        statement.bindLong(8, entity.getLength());
        statement.bindLong(9, entity.getDownloadedLength());
        final int _tmp = entity.isDownloading() ? 1 : 0;
        statement.bindLong(10, _tmp);
        statement.bindLong(11, entity.getId());
        statement.bindLong(12, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteBy = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM HanimeDownloadEntity WHERE (`videoCode` = ? AND `quality` = ?)";
        return _query;
      }
    };
    this.__preparedStmtOfPauseAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE HanimeDownloadEntity SET `isDownloading` = 0";
        return _query;
      }
    };
  }

  @Override
  public Object insert(final HanimeDownloadEntity entity,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfHanimeDownloadEntity.insert(entity);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object delete(final HanimeDownloadEntity entity,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfHanimeDownloadEntity.handle(entity);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object update(final HanimeDownloadEntity entity,
      final Continuation<? super Integer> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        int _total = 0;
        __db.beginTransaction();
        try {
          _total += __updateAdapterOfHanimeDownloadEntity.handle(entity);
          __db.setTransactionSuccessful();
          return _total;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object isExist(final String videoCode, final String quality,
      final Continuation<? super Boolean> $completion) {
    return RoomDatabaseKt.withTransaction(__db, (__cont) -> HanimeDownloadDao_Impl.super.isExist(videoCode, quality, __cont), $completion);
  }

  @Override
  public Object deleteBy(final String videoCode, final String quality,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteBy.acquire();
        int _argIndex = 1;
        _stmt.bindString(_argIndex, videoCode);
        _argIndex = 2;
        _stmt.bindString(_argIndex, quality);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteBy.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object pauseAll(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfPauseAll.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfPauseAll.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<HanimeDownloadEntity>> loadAllDownloadingHanime() {
    final String _sql = "SELECT * FROM HanimeDownloadEntity WHERE downloadedLength <> length ORDER BY id DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"HanimeDownloadEntity"}, new Callable<List<HanimeDownloadEntity>>() {
      @Override
      @NonNull
      public List<HanimeDownloadEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "coverUrl");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfAddDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addDate");
          final int _cursorIndexOfVideoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCode");
          final int _cursorIndexOfVideoUri = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUri");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUrl");
          final int _cursorIndexOfLength = CursorUtil.getColumnIndexOrThrow(_cursor, "length");
          final int _cursorIndexOfDownloadedLength = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedLength");
          final int _cursorIndexOfIsDownloading = CursorUtil.getColumnIndexOrThrow(_cursor, "isDownloading");
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final List<HanimeDownloadEntity> _result = new ArrayList<HanimeDownloadEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HanimeDownloadEntity _item;
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final long _tmpAddDate;
            _tmpAddDate = _cursor.getLong(_cursorIndexOfAddDate);
            final String _tmpVideoCode;
            _tmpVideoCode = _cursor.getString(_cursorIndexOfVideoCode);
            final String _tmpVideoUri;
            _tmpVideoUri = _cursor.getString(_cursorIndexOfVideoUri);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpVideoUrl;
            _tmpVideoUrl = _cursor.getString(_cursorIndexOfVideoUrl);
            final long _tmpLength;
            _tmpLength = _cursor.getLong(_cursorIndexOfLength);
            final long _tmpDownloadedLength;
            _tmpDownloadedLength = _cursor.getLong(_cursorIndexOfDownloadedLength);
            final boolean _tmpIsDownloading;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDownloading);
            _tmpIsDownloading = _tmp != 0;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item = new HanimeDownloadEntity(_tmpCoverUrl,_tmpTitle,_tmpAddDate,_tmpVideoCode,_tmpVideoUri,_tmpQuality,_tmpVideoUrl,_tmpLength,_tmpDownloadedLength,_tmpIsDownloading,_tmpId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<HanimeDownloadEntity>> loadAllDownloadedHanimeByTitle(final boolean ascending) {
    final String _sql = "SELECT * FROM HanimeDownloadEntity WHERE downloadedLength == length ORDER BY CASE WHEN ? THEN title END ASC, CASE WHEN NOT ? THEN title END DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final int _tmp = ascending ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp);
    _argIndex = 2;
    final int _tmp_1 = ascending ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp_1);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"HanimeDownloadEntity"}, new Callable<List<HanimeDownloadEntity>>() {
      @Override
      @NonNull
      public List<HanimeDownloadEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "coverUrl");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfAddDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addDate");
          final int _cursorIndexOfVideoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCode");
          final int _cursorIndexOfVideoUri = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUri");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUrl");
          final int _cursorIndexOfLength = CursorUtil.getColumnIndexOrThrow(_cursor, "length");
          final int _cursorIndexOfDownloadedLength = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedLength");
          final int _cursorIndexOfIsDownloading = CursorUtil.getColumnIndexOrThrow(_cursor, "isDownloading");
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final List<HanimeDownloadEntity> _result = new ArrayList<HanimeDownloadEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HanimeDownloadEntity _item;
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final long _tmpAddDate;
            _tmpAddDate = _cursor.getLong(_cursorIndexOfAddDate);
            final String _tmpVideoCode;
            _tmpVideoCode = _cursor.getString(_cursorIndexOfVideoCode);
            final String _tmpVideoUri;
            _tmpVideoUri = _cursor.getString(_cursorIndexOfVideoUri);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpVideoUrl;
            _tmpVideoUrl = _cursor.getString(_cursorIndexOfVideoUrl);
            final long _tmpLength;
            _tmpLength = _cursor.getLong(_cursorIndexOfLength);
            final long _tmpDownloadedLength;
            _tmpDownloadedLength = _cursor.getLong(_cursorIndexOfDownloadedLength);
            final boolean _tmpIsDownloading;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsDownloading);
            _tmpIsDownloading = _tmp_2 != 0;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item = new HanimeDownloadEntity(_tmpCoverUrl,_tmpTitle,_tmpAddDate,_tmpVideoCode,_tmpVideoUri,_tmpQuality,_tmpVideoUrl,_tmpLength,_tmpDownloadedLength,_tmpIsDownloading,_tmpId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<HanimeDownloadEntity>> loadAllDownloadedHanimeById(final boolean ascending) {
    final String _sql = "SELECT * FROM HanimeDownloadEntity WHERE downloadedLength == length ORDER BY CASE WHEN ? THEN id END ASC, CASE WHEN NOT ? THEN id END DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    final int _tmp = ascending ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp);
    _argIndex = 2;
    final int _tmp_1 = ascending ? 1 : 0;
    _statement.bindLong(_argIndex, _tmp_1);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"HanimeDownloadEntity"}, new Callable<List<HanimeDownloadEntity>>() {
      @Override
      @NonNull
      public List<HanimeDownloadEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "coverUrl");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfAddDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addDate");
          final int _cursorIndexOfVideoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCode");
          final int _cursorIndexOfVideoUri = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUri");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUrl");
          final int _cursorIndexOfLength = CursorUtil.getColumnIndexOrThrow(_cursor, "length");
          final int _cursorIndexOfDownloadedLength = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedLength");
          final int _cursorIndexOfIsDownloading = CursorUtil.getColumnIndexOrThrow(_cursor, "isDownloading");
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final List<HanimeDownloadEntity> _result = new ArrayList<HanimeDownloadEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final HanimeDownloadEntity _item;
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final long _tmpAddDate;
            _tmpAddDate = _cursor.getLong(_cursorIndexOfAddDate);
            final String _tmpVideoCode;
            _tmpVideoCode = _cursor.getString(_cursorIndexOfVideoCode);
            final String _tmpVideoUri;
            _tmpVideoUri = _cursor.getString(_cursorIndexOfVideoUri);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpVideoUrl;
            _tmpVideoUrl = _cursor.getString(_cursorIndexOfVideoUrl);
            final long _tmpLength;
            _tmpLength = _cursor.getLong(_cursorIndexOfLength);
            final long _tmpDownloadedLength;
            _tmpDownloadedLength = _cursor.getLong(_cursorIndexOfDownloadedLength);
            final boolean _tmpIsDownloading;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfIsDownloading);
            _tmpIsDownloading = _tmp_2 != 0;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item = new HanimeDownloadEntity(_tmpCoverUrl,_tmpTitle,_tmpAddDate,_tmpVideoCode,_tmpVideoUri,_tmpQuality,_tmpVideoUrl,_tmpLength,_tmpDownloadedLength,_tmpIsDownloading,_tmpId);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object findBy(final String videoCode, final String quality,
      final Continuation<? super HanimeDownloadEntity> $completion) {
    final String _sql = "SELECT * FROM HanimeDownloadEntity WHERE (`videoCode` = ? AND `quality` = ?) LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindString(_argIndex, videoCode);
    _argIndex = 2;
    _statement.bindString(_argIndex, quality);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<HanimeDownloadEntity>() {
      @Override
      @Nullable
      public HanimeDownloadEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCoverUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "coverUrl");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfAddDate = CursorUtil.getColumnIndexOrThrow(_cursor, "addDate");
          final int _cursorIndexOfVideoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "videoCode");
          final int _cursorIndexOfVideoUri = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUri");
          final int _cursorIndexOfQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "quality");
          final int _cursorIndexOfVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "videoUrl");
          final int _cursorIndexOfLength = CursorUtil.getColumnIndexOrThrow(_cursor, "length");
          final int _cursorIndexOfDownloadedLength = CursorUtil.getColumnIndexOrThrow(_cursor, "downloadedLength");
          final int _cursorIndexOfIsDownloading = CursorUtil.getColumnIndexOrThrow(_cursor, "isDownloading");
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final HanimeDownloadEntity _result;
          if (_cursor.moveToFirst()) {
            final String _tmpCoverUrl;
            _tmpCoverUrl = _cursor.getString(_cursorIndexOfCoverUrl);
            final String _tmpTitle;
            _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            final long _tmpAddDate;
            _tmpAddDate = _cursor.getLong(_cursorIndexOfAddDate);
            final String _tmpVideoCode;
            _tmpVideoCode = _cursor.getString(_cursorIndexOfVideoCode);
            final String _tmpVideoUri;
            _tmpVideoUri = _cursor.getString(_cursorIndexOfVideoUri);
            final String _tmpQuality;
            _tmpQuality = _cursor.getString(_cursorIndexOfQuality);
            final String _tmpVideoUrl;
            _tmpVideoUrl = _cursor.getString(_cursorIndexOfVideoUrl);
            final long _tmpLength;
            _tmpLength = _cursor.getLong(_cursorIndexOfLength);
            final long _tmpDownloadedLength;
            _tmpDownloadedLength = _cursor.getLong(_cursorIndexOfDownloadedLength);
            final boolean _tmpIsDownloading;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsDownloading);
            _tmpIsDownloading = _tmp != 0;
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _result = new HanimeDownloadEntity(_tmpCoverUrl,_tmpTitle,_tmpAddDate,_tmpVideoCode,_tmpVideoUri,_tmpQuality,_tmpVideoUrl,_tmpLength,_tmpDownloadedLength,_tmpIsDownloading,_tmpId);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
