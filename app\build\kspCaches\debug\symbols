{"src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\popup\\HTimePickerPopup.kt": ["HTimePickerPopup:com.yenaly.han1meviewer.ui.popup", "getImplLayoutId:com.yenaly.han1meviewer.ui.popup.HTimePickerPopup", "mode:com.yenaly.han1meviewer.ui.popup.HTimePickerPopup", "setMode:com.yenaly.han1meviewer.ui.popup.HTimePickerPopup", "onCreate:com.yenaly.han1meviewer.ui.popup.HTimePickerPopup"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\home\\download\\DownloadedFragment.kt": ["<init>:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadedFragment", "initData:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadedFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadedFragment", "viewModel:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadedFragment", "DownloadedFragment:com.yenaly.han1meviewer.ui.fragment.home.download", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadedFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadedFragment"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ActivityCloudflareBypassBinding.java": ["ActivityCloudflareBypassBinding:com.yenaly.han1meviewer.databinding", "inflate:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "toolbar:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "wvCloudflare:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "bind:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "btnResetUserAgent:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "tilUserAgent:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "btnApplyUserAgent:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "userAgentContainer:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "<init>:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "etUserAgent:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding", "srlCloudflare:com.yenaly.han1meviewer.databinding.ActivityCloudflareBypassBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\SearchViewModel.kt": ["month:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "SearchViewModel:com.yenaly.han1meviewer.ui.viewmodel", "genres:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "tagMap:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "brands:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "getHanimeSearchResult:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "page:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "insertSearchHistory:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "brandMap:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "tags:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "deleteSearchHistoryByKeyword:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "sortOptions:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "searchFlow:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "genre:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "sort:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "loadAllSearchHistories:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "query:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "durations:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "clearHanimeSearchResult:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "subscriptionBrand:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "duration:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "broad:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "year:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "searchStateFlow:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel", "deleteSearchHistory:com.yenaly.han1meviewer.ui.viewmodel.SearchViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HSearchTagAdapter.kt": ["onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HSearchTagAdapter", "checkedSet:com.yenaly.han1meviewer.ui.adapter.HSearchTagAdapter", "HSearchTagAdapter:com.yenaly.han1meviewer.ui.adapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HSearchTagAdapter", "<init>:com.yenaly.han1meviewer.ui.adapter.HSearchTagAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\DatabaseRepo.kt": ["appendKeyframe:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "HKeyframe:com.yenaly.han1meviewer.logic.DatabaseRepo", "delete:com.yenaly.han1meviewer.logic.DatabaseRepo.SearchHistory", "loadAll:com.yenaly.han1meviewer.logic.DatabaseRepo.SearchHistory", "pauseAll:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "isExist:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "observe:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "SearchHistory:com.yenaly.han1meviewer.logic.DatabaseRepo", "modifyKeyframe:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "delete:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "WatchHistory:com.yenaly.han1meviewer.logic.DatabaseRepo", "removeKeyframe:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "update:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "loadAllShared:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "loadAll:com.yenaly.han1meviewer.logic.DatabaseRepo.WatchHistory", "loadAllDownloadedHanime:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "insert:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "DatabaseRepo:com.yenaly.han1meviewer.logic", "<init>:com.yenaly.han1meviewer.logic.DatabaseRepo", "<init>:com.yenaly.han1meviewer.logic.DatabaseRepo.SearchHistory", "delete:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "deleteBy:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "update:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "insert:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "insert:com.yenaly.han1meviewer.logic.DatabaseRepo.WatchHistory", "<init>:com.yenaly.han1meviewer.logic.DatabaseRepo.WatchHistory", "deleteAll:com.yenaly.han1meviewer.logic.DatabaseRepo.WatchHistory", "loadAllDownloadingHanime:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "insert:com.yenaly.han1meviewer.logic.DatabaseRepo.SearchHistory", "deleteByKeyword:com.yenaly.han1meviewer.logic.DatabaseRepo.SearchHistory", "HanimeDownload:com.yenaly.han1meviewer.logic.DatabaseRepo", "loadAll:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "<init>:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "delete:com.yenaly.han1meviewer.logic.DatabaseRepo.WatchHistory", "findBy:com.yenaly.han1meviewer.logic.DatabaseRepo.HKeyframe", "<init>:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload", "findBy:com.yenaly.han1meviewer.logic.DatabaseRepo.HanimeDownload"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\FragmentTabViewPagerOnlyBinding.java": ["FragmentTabViewPagerOnlyBinding:com.yenaly.han1meviewer.databinding", "inflate:com.yenaly.han1meviewer.databinding.FragmentTabViewPagerOnlyBinding", "coordinator:com.yenaly.han1meviewer.databinding.FragmentTabViewPagerOnlyBinding", "toolbar:com.yenaly.han1meviewer.databinding.FragmentTabViewPagerOnlyBinding", "<init>:com.yenaly.han1meviewer.databinding.FragmentTabViewPagerOnlyBinding", "bind:com.yenaly.han1meviewer.databinding.FragmentTabViewPagerOnlyBinding", "tabLayout:com.yenaly.han1meviewer.databinding.FragmentTabViewPagerOnlyBinding", "viewPager:com.yenaly.han1meviewer.databinding.FragmentTabViewPagerOnlyBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\CommentViewModel.kt": ["videoCommentStateFlow:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "postReply:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "getCommentReply:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "CommentViewModel:com.yenaly.han1meviewer.ui.viewmodel", "videoReplyFlow:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "likeChildComment:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "getComment:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "handleCommentLike:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "videoCommentFlow:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "updateComments:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "postReplyFlow:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "postCommentFlow:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "code:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "videoReplyStateFlow:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "likeComment:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "currentUserId:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "commentLikeFlow:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel", "postComment:com.yenaly.han1meviewer.ui.viewmodel.CommentViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Parcels.kt": ["writeBooleanCompat:com.yenaly.han1meviewer.util", "readBooleanCompat:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\VideoComments.kt": ["incLikesCount:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "thumbUp:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "realReplyId:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "unlikeCommentStatus:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment.POST", "isPositive:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment.POST", "commentLikesCount:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment.POST", "avatar:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "likeUserId:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment.POST", "videoComment:com.yenaly.han1meviewer.logic.model.VideoComments", "VideoComment:com.yenaly.han1meviewer.logic.model.VideoComments", "commentPosition:com.yenaly.han1meviewer.logic.model.VideoCommentArgs", "foreignId:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment.POST", "content:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "commentLikesSum:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment.POST", "VideoComments:com.yenaly.han1meviewer.logic.model", "decLikesCount:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "id:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "realLikesCount:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "POST:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "likeCommentStatus:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment.POST", "username:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "date:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "hasMoreReplies:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "isChildComment:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "comment:com.yenaly.han1meviewer.logic.model.VideoCommentArgs", "csrfToken:com.yenaly.han1meviewer.logic.model.VideoComments", "currentUserId:com.yenaly.han1meviewer.logic.model.VideoComments", "post:com.yenaly.han1meviewer.logic.model.VideoComments.VideoComment", "VideoCommentArgs:com.yenaly.han1meviewer.logic.model", "isPositive:com.yenaly.han1meviewer.logic.model.VideoCommentArgs"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\settings\\HKeyframesFragment.kt": ["viewModel:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframesFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframesFragment", "initData:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframesFragment", "HKeyframesFragment:com.yenaly.han1meviewer.ui.fragment.settings", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframesFragment", "onStart:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframesFragment", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframesFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframesFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\dao\\SearchHistoryDao.kt": ["delete:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao", "insertOrUpdate:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao", "<init>:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao", "loadAll:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao", "find:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao", "insert:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao", "deleteByKeyword:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao", "SearchHistoryDao:com.yenaly.han1meviewer.logic.dao"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Views.kt": ["getOrCreateBadgeOnTextViewAt:com.yenaly.han1meviewer.util", "getTextViewAt:com.yenaly.han1meviewer.util", "setGravity:com.yenaly.han1meviewer.util", "setDrawableTop:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\SearchTag.kt": ["genres:com.yenaly.han1meviewer.logic.model.SearchTag", "years:com.yenaly.han1meviewer.logic.model.SearchTag.ReleaseDate", "SearchTag:com.yenaly.han1meviewer.logic.model", "sortOptions:com.yenaly.han1meviewer.logic.model.SearchTag", "durationOptions:com.yenaly.han1meviewer.logic.model.SearchTag", "months:com.yenaly.han1meviewer.logic.model.SearchTag.ReleaseDate", "ReleaseDate:com.yenaly.han1meviewer.logic.model.SearchTag", "tags:com.yenaly.han1meviewer.logic.model.SearchTag", "releaseDates:com.yenaly.han1meviewer.logic.model.SearchTag", "brands:com.yenaly.han1meviewer.logic.model.SearchTag"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\LoginNeededFragmentMixin.kt": ["LoginNeededFragmentMixin:com.yenaly.han1meviewer.ui.fragment", "checkLogin:com.yenaly.han1meviewer.ui.fragment.LoginNeededFragmentMixin"], "src\\main\\java\\com\\yenaly\\han1meviewer\\HAdvancedSearch.kt": ["GENRE:com.yenaly.han1meviewer.HAdvancedSearch", "<init>:com.yenaly.han1meviewer.HAdvancedSearch.SORT", "HAdvancedSearch:com.yenaly.han1meviewer", "AdvancedSearchMap:com.yenaly.han1meviewer", "<init>:com.yenaly.han1meviewer.HAdvancedSearch.YEAR", "TAGS:com.yenaly.han1meviewer.HAdvancedSearch", "<init>:com.yenaly.han1meviewer.HAdvancedSearch.QUERY", "YEAR:com.yenaly.han1meviewer.HAdvancedSearch", "DURATION:com.yenaly.han1meviewer.HAdvancedSearch", "<init>:com.yenaly.han1meviewer.HAdvancedSearch.TAGS", "<init>:com.yenaly.han1meviewer.HAdvancedSearch.MONTH", "<init>:com.yenaly.han1meviewer.HAdvancedSearch.BRANDS", "<init>:com.yenaly.han1meviewer.HAdvancedSearch.DURATION", "BRANDS:com.yenaly.han1meviewer.HAdvancedSearch", "MONTH:com.yenaly.han1meviewer.HAdvancedSearch", "advancedSearchMapOf:com.yenaly.han1meviewer", "<init>:com.yenaly.han1meviewer.HAdvancedSearch.GENRE", "SORT:com.yenaly.han1meviewer.HAdvancedSearch", "QUERY:com.yenaly.han1meviewer.HAdvancedSearch"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ActivitySearchBinding.java": ["state:com.yenaly.han1meviewer.databinding.ActivitySearchBinding", "inflate:com.yenaly.han1meviewer.databinding.ActivitySearchBinding", "ActivitySearchBinding:com.yenaly.han1meviewer.databinding", "searchSrl:com.yenaly.han1meviewer.databinding.ActivitySearchBinding", "coordinator:com.yenaly.han1meviewer.databinding.ActivitySearchBinding", "searchBar:com.yenaly.han1meviewer.databinding.ActivitySearchBinding", "searchHeader:com.yenaly.han1meviewer.databinding.ActivitySearchBinding", "searchRv:com.yenaly.han1meviewer.databinding.ActivitySearchBinding", "<init>:com.yenaly.han1meviewer.databinding.ActivitySearchBinding", "bind:com.yenaly.han1meviewer.databinding.ActivitySearchBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemHanimeDownloadingBinding.java": ["ItemHanimeDownloadingBinding:com.yenaly.han1meviewer.databinding", "Barrier:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "btnStart:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "btnCancel:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "inflate:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "<init>:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "tvTitle:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "tvQuality:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "tvSize:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "ivCover:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "bind:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "llProgress:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "pbProgress:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding", "tvProgress:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadingBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HSubscriptionAdapter.kt": ["Companion:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter", "getChangePayload:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter.COMPARATOR", "CHECK:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter.Companion", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter", "HSubscriptionAdapter:com.yenaly.han1meviewer.ui.adapter", "DELETE:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter.Companion", "areItemsTheSame:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter.COMPARATOR", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter", "<init>:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter.Companion", "<init>:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter", "areContentsTheSame:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter.COMPARATOR", "<init>:com.yenaly.han1meviewer.ui.adapter.HSubscriptionAdapter.COMPARATOR"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Adapters.kt": ["awaitSubmitList:com.yenaly.han1meviewer.util", "notNull:com.yenaly.han1meviewer.util", "setStateViewLayout:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\VideoColumnTitleAdapter.kt": ["onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.VideoColumnTitleAdapter", "subtitle:com.yenaly.han1meviewer.ui.adapter.VideoColumnTitleAdapter", "title:com.yenaly.han1meviewer.ui.adapter.VideoColumnTitleAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.VideoColumnTitleAdapter", "VideoColumnTitleAdapter:com.yenaly.han1meviewer.ui.adapter", "onMoreHanimeListener:com.yenaly.han1meviewer.ui.adapter.VideoColumnTitleAdapter"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ActivitySettingsBinding.java": ["inflate:com.yenaly.han1meviewer.databinding.ActivitySettingsBinding", "bind:com.yenaly.han1meviewer.databinding.ActivitySettingsBinding", "toolbar:com.yenaly.han1meviewer.databinding.ActivitySettingsBinding", "fcvSettings:com.yenaly.han1meviewer.databinding.ActivitySettingsBinding", "<init>:com.yenaly.han1meviewer.databinding.ActivitySettingsBinding", "ActivitySettingsBinding:com.yenaly.han1meviewer.databinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\HanimeManager.kt": ["videoUrlRegex:com.yenaly.han1meviewer", "login:com.yenaly.han1meviewer", "getHanimeVideoDownloadLink:com.yenaly.han1meviewer", "toVideoCode:com.yenaly.han1meviewer", "logout:com.yenaly.han1meviewer", "pienization:com.yenaly.han1meviewer", "HJson:com.yenaly.han1meviewer", "hanimeSpannable:com.yenaly.han1meviewer", "getHanimeVideoLink:com.yenaly.han1meviewer"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\dao\\HanimeDownloadedDao.kt": ["loadDownloadedHanimeByVideoCode:com.yenaly.han1meviewer.logic.dao.HanimeDownloadedDao", "loadAllDownloadedHanime:com.yenaly.han1meviewer.logic.dao.HanimeDownloadedDao", "insertDownloadedHanime:com.yenaly.han1meviewer.logic.dao.HanimeDownloadedDao", "deleteDownloadedHanimeByVideoCode:com.yenaly.han1meviewer.logic.dao.HanimeDownloadedDao", "updateDownloadedHanimeQualityByVideoCode:com.yenaly.han1meviewer.logic.dao.HanimeDownloadedDao", "HanimeDownloadedDao:com.yenaly.han1meviewer.logic.dao", "<init>:com.yenaly.han1meviewer.logic.dao.HanimeDownloadedDao", "updateDownloadedHanime:com.yenaly.han1meviewer.logic.dao.HanimeDownloadedDao"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\github\\Commit.kt": ["CommitAuthor:com.yenaly.han1meviewer.logic.model.github.CommitComparison.Commit.CommitDetail", "Commit:com.yenaly.han1meviewer.logic.model.github.CommitComparison", "message:com.yenaly.han1meviewer.logic.model.github.CommitComparison.Commit.CommitDetail", "CommitDetail:com.yenaly.han1meviewer.logic.model.github.CommitComparison.Commit", "name:com.yenaly.han1meviewer.logic.model.github.CommitComparison.Commit.CommitDetail.CommitAuthor", "CommitComparison:com.yenaly.han1meviewer.logic.model.github", "commits:com.yenaly.han1meviewer.logic.model.github.CommitComparison", "commit:com.yenaly.han1meviewer.logic.model.github.CommitComparison.Commit", "author:com.yenaly.han1meviewer.logic.model.github.CommitComparison.Commit.CommitDetail"], "src\\main\\java\\com\\yenaly\\han1meviewer\\viewmodel\\CloudflareBypassViewModel.kt": ["CloudflareBypassViewModel:com.yenaly.han1meviewer.viewmodel", "verificationStatus:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel", "getCurrentUserAgent:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel", "resetVerification:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel", "cfClearance:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel", "setCustomUserAgent:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel", "customUserAgent:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel", "setCfClearance:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel", "resetUserAgent:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel", "<init>:com.yenaly.han1meviewer.viewmodel.CloudflareBypassViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\service\\HanimeCommentService.kt": ["HanimeCommentService:com.yenaly.han1meviewer.logic.network.service", "getCommentReply:com.yenaly.han1meviewer.logic.network.service.HanimeCommentService", "getComments:com.yenaly.han1meviewer.logic.network.service.HanimeCommentService", "likeComment:com.yenaly.han1meviewer.logic.network.service.HanimeCommentService", "postCommentReply:com.yenaly.han1meviewer.logic.network.service.HanimeCommentService", "postComment:com.yenaly.han1meviewer.logic.network.service.HanimeCommentService"], "src\\main\\java\\com\\yenaly\\han1meviewer\\HanimeApplication.kt": ["<init>:com.yenaly.han1meviewer.HanimeApplication.Companion", "isDefaultCrashHandlerEnabled:com.yenaly.han1meviewer.HanimeApplication", "HanimeApplication:com.yenaly.han1meviewer", "onCreate:com.yenaly.han1meviewer.HanimeApplication", "Companion:com.yenaly.han1meviewer.HanimeApplication", "TAG:com.yenaly.han1meviewer.HanimeApplication.Companion", "<init>:com.yenaly.han1meviewer.HanimeApplication"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\MainViewModel.kt": ["deleteWatchHistory:com.yenaly.han1meviewer.ui.viewmodel.MainViewModel", "MainViewModel:com.yenaly.han1meviewer.ui.viewmodel", "loadAllWatchHistories:com.yenaly.han1meviewer.ui.viewmodel.MainViewModel", "getHomePage:com.yenaly.han1meviewer.ui.viewmodel.MainViewModel", "homePageFlow:com.yenaly.han1meviewer.ui.viewmodel.MainViewModel", "deleteAllWatchHistories:com.yenaly.han1meviewer.ui.viewmodel.MainViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\HanimeVideo.kt": ["isFav:com.yenaly.han1meviewer.logic.model.HanimeVideo", "video:com.yenaly.han1meviewer.logic.model.HanimeVideo.Playlist", "Artist:com.yenaly.han1meviewer.logic.model.HanimeVideo", "favTimes:com.yenaly.han1meviewer.logic.model.HanimeVideo", "POST:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist", "genre:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist", "chineseTitle:com.yenaly.han1meviewer.logic.model.HanimeVideo", "title:com.yenaly.han1meviewer.logic.model.HanimeVideo", "myList:com.yenaly.han1meviewer.logic.model.HanimeVideo", "playlist:com.yenaly.han1meviewer.logic.model.HanimeVideo", "tags:com.yenaly.han1meviewer.logic.model.HanimeVideo", "csrfToken:com.yenaly.han1meviewer.logic.model.HanimeVideo", "isWatchLater:com.yenaly.han1meviewer.logic.model.HanimeVideo.MyList", "decFavTime:com.yenaly.han1meviewer.logic.model.HanimeVideo", "introduction:com.yenaly.han1meviewer.logic.model.HanimeVideo", "uploadTime:com.yenaly.han1meviewer.logic.model.HanimeVideo", "titleArray:com.yenaly.han1meviewer.logic.model.HanimeVideo.MyList", "isSelectedArray:com.yenaly.han1meviewer.logic.model.HanimeVideo.MyList", "post:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist", "isSubscribed:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist", "code:com.yenaly.han1meviewer.logic.model.HanimeVideo.MyList.MyListInfo", "playlistName:com.yenaly.han1meviewer.logic.model.HanimeVideo.Playlist", "views:com.yenaly.han1meviewer.logic.model.HanimeVideo", "videoUrls:com.yenaly.han1meviewer.logic.model.HanimeVideo", "relatedHanimes:com.yenaly.han1meviewer.logic.model.HanimeVideo", "currentUserId:com.yenaly.han1meviewer.logic.model.HanimeVideo", "title:com.yenaly.han1meviewer.logic.model.HanimeVideo.MyList.MyListInfo", "artist:com.yenaly.han1meviewer.logic.model.HanimeVideo", "uploadTimeMillis:com.yenaly.han1meviewer.logic.model.HanimeVideo", "avatarUrl:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist", "isSelected:com.yenaly.han1meviewer.logic.model.HanimeVideo.MyList.MyListInfo", "MyList:com.yenaly.han1meviewer.logic.model.HanimeVideo", "MyListInfo:com.yenaly.han1meviewer.logic.model.HanimeVideo.MyList", "isSubscribed:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist.POST", "artistId:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist.POST", "myListInfo:com.yenaly.han1meviewer.logic.model.HanimeVideo.MyList", "Playlist:com.yenaly.han1meviewer.logic.model.HanimeVideo", "name:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist", "HanimeVideo:com.yenaly.han1meviewer.logic.model", "coverUrl:com.yenaly.han1meviewer.logic.model.HanimeVideo", "incFavTime:com.yenaly.han1meviewer.logic.model.HanimeVideo", "userId:com.yenaly.han1meviewer.logic.model.HanimeVideo.Artist.POST"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\NetworkRepo.kt": ["getHanimeVideo:com.yenaly.han1meviewer.logic.NetworkRepo", "addToMyFavVideo:com.yenaly.han1meviewer.logic.NetworkRepo", "getLatestVersion:com.yenaly.han1meviewer.logic.NetworkRepo", "getMyListItems:com.yenaly.han1meviewer.logic.NetworkRepo", "likeComment:com.yenaly.han1meviewer.logic.NetworkRepo", "subscribeArtist:com.yenaly.han1meviewer.logic.NetworkRepo", "createPlaylist:com.yenaly.han1meviewer.logic.NetworkRepo", "getHanimePreview:com.yenaly.han1meviewer.logic.NetworkRepo", "addToMyList:com.yenaly.han1meviewer.logic.NetworkRepo", "deleteMyListItems:com.yenaly.han1meviewer.logic.NetworkRepo", "getSubscriptions:com.yenaly.han1meviewer.logic.NetworkRepo", "getPlaylists:com.yenaly.han1meviewer.logic.NetworkRepo", "getCommentReply:com.yenaly.han1meviewer.logic.NetworkRepo", "getHanimeSearchResult:com.yenaly.han1meviewer.logic.NetworkRepo", "modifyPlaylist:com.yenaly.han1meviewer.logic.NetworkRepo", "getComments:com.yenaly.han1meviewer.logic.NetworkRepo", "postComment:com.yenaly.han1meviewer.logic.NetworkRepo", "getHomePage:com.yenaly.han1meviewer.logic.NetworkRepo", "NetworkRepo:com.yenaly.han1meviewer.logic", "postCommentReply:com.yenaly.han1meviewer.logic.NetworkRepo", "<init>:com.yenaly.han1meviewer.logic.NetworkRepo", "login:com.yenaly.han1meviewer.logic.NetworkRepo"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\video\\HMediaKernel.kt": ["MediaPlayer:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type", "Companion:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type", "ExoMediaKernel:com.yenaly.han1meviewer.ui.view.video", "setVolume:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "onSurfaceTextureDestroyed:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "ExoPlayer:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type", "<init>:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type.MediaPlayer", "getCurrentPosition:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "onTimelineChanged:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "<init>:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel.OnBufferingUpdate", "onPlaybackStateChanged:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "onIsLoadingChanged:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "TAG:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel.Companion", "onPlayerError:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "SystemMediaKernel:com.yenaly.han1meviewer.ui.view.video", "setSpeed:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "onSurfaceTextureUpdated:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "pause:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "release:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "setSurface:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "onPositionDiscontinuity:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "Type:com.yenaly.han1meviewer.ui.view.video.HMediaKernel", "<init>:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type.ExoPlayer", "Companion:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "start:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "<init>:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type.GSYPlayer", "isPlaying:com.yenaly.han1meviewer.ui.view.video.SystemMediaKernel", "GSYPlayer:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type", "<init>:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel.Companion", "onVideoSizeChanged:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "HMediaKernel:com.yenaly.han1meviewer.ui.view.video", "onRenderedFirstFrame:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "run:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel.OnBufferingUpdate", "pause:com.yenaly.han1meviewer.ui.view.video.SystemMediaKernel", "onSurfaceTextureAvailable:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "onSurfaceTextureSizeChanged:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "getDuration:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "onPlayWhenReadyChanged:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "prepare:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "fromString:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type.Companion", "seekTo:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel", "clazz:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type", "<init>:com.yenaly.han1meviewer.ui.view.video.HMediaKernel.Type.Companion", "setSpeed:com.yenaly.han1meviewer.ui.view.video.SystemMediaKernel", "onVideoSizeChanged:com.yenaly.han1meviewer.ui.view.video.SystemMediaKernel", "isPlaying:com.yenaly.han1meviewer.ui.view.video.ExoMediaKernel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\home\\WatchHistoryFragment.kt": ["<init>:com.yenaly.han1meviewer.ui.fragment.home.WatchHistoryFragment", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.home.WatchHistoryFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.home.WatchHistoryFragment", "initData:com.yenaly.han1meviewer.ui.fragment.home.WatchHistoryFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.home.WatchHistoryFragment", "viewModel:com.yenaly.han1meviewer.ui.fragment.home.WatchHistoryFragment", "WatchHistoryFragment:com.yenaly.han1meviewer.ui.fragment.home"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\MyListViewModel.kt": ["subscription:com.yenaly.han1meviewer.ui.viewmodel.MyListViewModel", "watchLater:com.yenaly.han1meviewer.ui.viewmodel.MyListViewModel", "playlist:com.yenaly.han1meviewer.ui.viewmodel.MyListViewModel", "fav:com.yenaly.han1meviewer.ui.viewmodel.MyListViewModel", "MyListViewModel:com.yenaly.han1meviewer.ui.viewmodel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\BaseSingleDifferAdapter.kt": ["onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter", "submit:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter", "set:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter", "removeAtRange:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter", "addAll:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter", "remove:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter", "add:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter", "item:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter", "BaseSingleDifferAdapter:com.yenaly.han1meviewer.ui.adapter", "removeAt:com.yenaly.han1meviewer.ui.adapter.BaseSingleDifferAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\dao\\WatchHistoryDao.kt": ["find:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao", "WatchHistoryDao:com.yenaly.han1meviewer.logic.dao", "loadAll:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao", "<init>:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao", "insertOrUpdate:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao", "insert:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao", "delete:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao", "deleteAll:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\FragmentCommentBinding.java": ["rvComment:com.yenaly.han1meviewer.databinding.FragmentCommentBinding", "btnComment:com.yenaly.han1meviewer.databinding.FragmentCommentBinding", "bind:com.yenaly.han1meviewer.databinding.FragmentCommentBinding", "srlComment:com.yenaly.han1meviewer.databinding.FragmentCommentBinding", "FragmentCommentBinding:com.yenaly.han1meviewer.databinding", "state:com.yenaly.han1meviewer.databinding.FragmentCommentBinding", "inflate:com.yenaly.han1meviewer.databinding.FragmentCommentBinding", "<init>:com.yenaly.han1meviewer.databinding.FragmentCommentBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\BlurTransformation.kt": ["cacheKey:com.yenaly.han1meviewer.ui.view.BlurTransformation", "transform:com.yenaly.han1meviewer.ui.view.BlurTransformation", "<init>:com.yenaly.han1meviewer.ui.view.BlurTransformation.Companion", "BlurTransformation:com.yenaly.han1meviewer.ui.view"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\state\\PageLoadingState.kt": ["info:com.yenaly.han1meviewer.logic.state.PageLoadingState.Success", "Success:com.yenaly.han1meviewer.logic.state.PageLoadingState", "Loading:com.yenaly.han1meviewer.logic.state.PageLoadingState", "NoMoreData:com.yenaly.han1meviewer.logic.state.PageLoadingState", "<init>:com.yenaly.han1meviewer.logic.state.PageLoadingState.NoMoreData", "throwable:com.yenaly.han1meviewer.logic.state.PageLoadingState.Error", "<init>:com.yenaly.han1meviewer.logic.state.PageLoadingState", "<init>:com.yenaly.han1meviewer.logic.state.PageLoadingState.Loading", "Error:com.yenaly.han1meviewer.logic.state.PageLoadingState", "PageLoadingState:com.yenaly.han1meviewer.logic.state"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\FragmentHKeyframesBinding.java": ["rvKeyframe:com.yenaly.han1meviewer.databinding.FragmentHKeyframesBinding", "btnUp:com.yenaly.han1meviewer.databinding.FragmentHKeyframesBinding", "bind:com.yenaly.han1meviewer.databinding.FragmentHKeyframesBinding", "FragmentHKeyframesBinding:com.yenaly.han1meviewer.databinding", "inflate:com.yenaly.han1meviewer.databinding.FragmentHKeyframesBinding", "btnDown:com.yenaly.han1meviewer.databinding.FragmentHKeyframesBinding", "<init>:com.yenaly.han1meviewer.databinding.FragmentHKeyframesBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\PlaylistHeader.kt": ["PlaylistHeader:com.yenaly.han1meviewer.ui.view", "title:com.yenaly.han1meviewer.ui.view.PlaylistHeader", "onDeleteActionListener:com.yenaly.han1meviewer.ui.view.PlaylistHeader", "description:com.yenaly.han1meviewer.ui.view.PlaylistHeader", "onChangedListener:com.yenaly.han1meviewer.ui.view.PlaylistHeader"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\yenaly\\han1meviewer\\logic\\dao\\HKeyframeDao_Impl.java": ["loadAll:com.yenaly.han1meviewer.logic.dao.HKeyframeDao_Impl", "observe:com.yenaly.han1meviewer.logic.dao.HKeyframeDao_Impl", "<init>:com.yenaly.han1meviewer.logic.dao.HKeyframeDao_Impl", "findBy:com.yenaly.han1meviewer.logic.dao.HKeyframeDao_Impl", "delete:com.yenaly.han1meviewer.logic.dao.HKeyframeDao_Impl", "insert:com.yenaly.han1meviewer.logic.dao.HKeyframeDao_Impl", "HKeyframeDao_Impl:com.yenaly.han1meviewer.logic.dao", "update:com.yenaly.han1meviewer.logic.dao.HKeyframeDao_Impl", "getRequiredConverters:com.yenaly.han1meviewer.logic.dao.HKeyframeDao_Impl"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\HCookieJar.kt": ["<init>:com.yenaly.han1meviewer.logic.network.HCookieJar", "cookieMap:com.yenaly.han1meviewer.logic.network.HCookieJar.Companion", "loadForRequest:com.yenaly.han1meviewer.logic.network.HCookieJar", "addCookie:com.yenaly.han1meviewer.logic.network.HCookieJar", "hasExpired:com.yenaly.han1meviewer.logic.network", "Companion:com.yenaly.han1meviewer.logic.network.HCookieJar", "getCfClearance:com.yenaly.han1meviewer.logic.network.HCookieJar.Companion", "HCookieJar:com.yenaly.han1meviewer.logic.network", "hasCfClearance:com.yenaly.han1meviewer.logic.network.HCookieJar.Companion", "saveFromResponse:com.yenaly.han1meviewer.logic.network.HCookieJar", "logAllCookies:com.yenaly.han1meviewer.logic.network.HCookieJar.Companion", "getCookie:com.yenaly.han1meviewer.logic.network.HCookieJar.Companion", "<init>:com.yenaly.han1meviewer.logic.network.HCookieJar.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\state\\WebsiteState.kt": ["<init>:com.yenaly.han1meviewer.logic.state.WebsiteState", "Error:com.yenaly.han1meviewer.logic.state.WebsiteState", "WebsiteState:com.yenaly.han1meviewer.logic.state", "info:com.yenaly.han1meviewer.logic.state.WebsiteState.Success", "Success:com.yenaly.han1meviewer.logic.state.WebsiteState", "throwable:com.yenaly.han1meviewer.logic.state.WebsiteState.Error", "Loading:com.yenaly.han1meviewer.logic.state.WebsiteState", "<init>:com.yenaly.han1meviewer.logic.state.WebsiteState.Loading"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ActivityMainBinding.java": ["fcvMain:com.yenaly.han1meviewer.databinding.ActivityMainBinding", "inflate:com.yenaly.han1meviewer.databinding.ActivityMainBinding", "dlMain:com.yenaly.han1meviewer.databinding.ActivityMainBinding", "nvMain:com.yenaly.han1meviewer.databinding.ActivityMainBinding", "bind:com.yenaly.han1meviewer.databinding.ActivityMainBinding", "ActivityMainBinding:com.yenaly.han1meviewer.databinding", "<init>:com.yenaly.han1meviewer.databinding.ActivityMainBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemHanimePreviewNewsBinding.java": ["inflate:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "tvBrand:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "tvReleaseDate:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "tvIntroduction:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "<init>:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "tvVideoTitle:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "ivCover:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "rvPreview:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "bind:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "tags:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "tvTitle:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "ItemHanimePreviewNewsBinding:com.yenaly.han1meviewer.databinding", "ivCoverBig:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding", "BARRIER:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ActivityPreviewBinding.java": ["<init>:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "nsvPreview:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "bind:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "appBar:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "llTour:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "fabNext:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "rvTourSimplified:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "vpNews:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "toolbar:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "fabPrevious:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "collapsingToolbar:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "cover:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding", "ActivityPreviewBinding:com.yenaly.han1meviewer.databinding", "inflate:com.yenaly.han1meviewer.databinding.ActivityPreviewBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HanimeMyListVideoAdapter.kt": ["<init>:com.yenaly.han1meviewer.ui.adapter.HanimeMyListVideoAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeMyListVideoAdapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeMyListVideoAdapter", "HanimeMyListVideoAdapter:com.yenaly.han1meviewer.ui.adapter", "Companion:com.yenaly.han1meviewer.ui.adapter.HanimeMyListVideoAdapter", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.HanimeMyListVideoAdapter.Companion", "<init>:com.yenaly.han1meviewer.ui.adapter.HanimeMyListVideoAdapter.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\HInitializer.kt": ["<init>:com.yenaly.han1meviewer.HInitializer", "HInitializer:com.yenaly.han1meviewer", "create:com.yenaly.han1meviewer.HInitializer"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\entity\\HanimeDownloadedEntity.kt": ["coverUrl:com.yenaly.han1meviewer.logic.entity.HanimeDownloadedEntity", "title:com.yenaly.han1meviewer.logic.entity.HanimeDownloadedEntity", "releaseDate:com.yenaly.han1meviewer.logic.entity.HanimeDownloadedEntity", "quality:com.yenaly.han1meviewer.logic.entity.HanimeDownloadedEntity", "HanimeDownloadedEntity:com.yenaly.han1meviewer.logic.entity", "videoUri:com.yenaly.han1meviewer.logic.entity.HanimeDownloadedEntity", "id:com.yenaly.han1meviewer.logic.entity.HanimeDownloadedEntity", "addDate:com.yenaly.han1meviewer.logic.entity.HanimeDownloadedEntity", "videoCode:com.yenaly.han1meviewer.logic.entity.HanimeDownloadedEntity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\FirebaseConstants.kt": ["H_KEYFRAMES:com.yenaly.han1meviewer.FirebaseConstants", "remoteConfigDefaults:com.yenaly.han1meviewer.FirebaseConstants", "ADV_SEARCH_OPT:com.yenaly.han1meviewer.FirebaseConstants", "ENABLE_CI_UPDATE:com.yenaly.han1meviewer.FirebaseConstants", "FirebaseConstants:com.yenaly.han1meviewer", "<init>:com.yenaly.han1meviewer.FirebaseConstants", "VERSION_SOURCE:com.yenaly.han1meviewer.FirebaseConstants", "LOGIN_STATE:com.yenaly.han1meviewer.FirebaseConstants", "APP_LANGUAGE:com.yenaly.han1meviewer.FirebaseConstants"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\pref\\HPrivacyPreference.kt": ["privacyDialog:com.yenaly.han1meviewer.ui.view.pref.HPrivacyPreference", "onClick:com.yenaly.han1meviewer.ui.view.pref.HPrivacyPreference", "HPrivacyPreference:com.yenaly.han1meviewer.ui.view.pref"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\dao\\HKeyframeDao.kt": ["appendKeyframe:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "insert:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "update:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "observe:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "loadAll:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "delete:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "HKeyframeDao:com.yenaly.han1meviewer.logic.dao", "removeKeyframe:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "<init>:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "modifyKeyframe:com.yenaly.han1meviewer.logic.dao.HKeyframeDao", "findBy:com.yenaly.han1meviewer.logic.dao.HKeyframeDao"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\AlertDialogs.kt": ["getDialogDefaultDrawable:com.yenaly.han1meviewer.util", "createAlertDialog:com.yenaly.han1meviewer.util", "showWithBlurEffect:com.yenaly.han1meviewer.util", "await:com.yenaly.han1meviewer.util", "showAlertDialog:com.yenaly.han1meviewer.util", "createDecorBlurEffect:com.yenaly.han1meviewer.util"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\FragmentHomePageBinding.java": ["homePageSrl:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "tvBannerTitle:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "banner:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "rv:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "bind:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "aColor:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "header:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "btnBanner:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "state:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "tvBannerDesc:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "inflate:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "coordinator:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "appBar:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "ivBanner:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "<init>:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "FragmentHomePageBinding:com.yenaly.han1meviewer.databinding", "collapsingToolbar:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "toolbar:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding", "cover:com.yenaly.han1meviewer.databinding.FragmentHomePageBinding"], "build\\generated\\data_binding_trigger\\debug\\com\\yenaly\\han1meviewer\\DataBindingTriggerClass.java": ["<init>:com.yenaly.han1meviewer.DataBindingTriggerClass", "DataBindingTriggerClass:com.yenaly.han1meviewer"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HKeyframesRvAdapter.kt": ["COMPARATOR:com.yenaly.han1meviewer.ui.adapter.HKeyframesRvAdapter.Companion", "<init>:com.yenaly.han1meviewer.ui.adapter.HKeyframesRvAdapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HKeyframeRvAdapter", "Companion:com.yenaly.han1meviewer.ui.adapter.HKeyframesRvAdapter", "isLocal:com.yenaly.han1meviewer.ui.adapter.HKeyframeRvAdapter", "HKeyframesRvAdapter:com.yenaly.han1meviewer.ui.adapter", "<init>:com.yenaly.han1meviewer.ui.adapter.HKeyframesRvAdapter.Companion", "<init>:com.yenaly.han1meviewer.ui.adapter.HKeyframeRvAdapter.Companion", "HKeyframeRvAdapter:com.yenaly.han1meviewer.ui.adapter", "Companion:com.yenaly.han1meviewer.ui.adapter.HKeyframeRvAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HKeyframesRvAdapter", "isShared:com.yenaly.han1meviewer.ui.adapter.HKeyframeRvAdapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HKeyframesRvAdapter", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.HKeyframeRvAdapter.Companion", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HKeyframeRvAdapter"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\yenaly\\han1meviewer\\logic\\dao\\HanimeDownloadDao_Impl.java": ["<init>:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "pauseAll:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "findBy:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "delete:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "update:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "getRequiredConverters:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "loadAllDownloadedHanimeByTitle:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "HanimeDownloadDao_Impl:com.yenaly.han1meviewer.logic.dao", "isExist:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "loadAllDownloadedHanimeById:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "insert:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "deleteBy:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl", "loadAllDownloadingHanime:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao_Impl"], "src\\main\\java\\com\\yenaly\\han1meviewer\\VideoCoverSize.kt": ["videoInOneLine:com.yenaly.han1meviewer.VideoCoverSize.Simplified", "Normal:com.yenaly.han1meviewer.VideoCoverSize", "videoInOneLine:com.yenaly.han1meviewer.VideoCoverSize.Normal", "VideoCoverSize:com.yenaly.han1meviewer", "resizeForVideoCover:com.yenaly.han1meviewer.VideoCoverSize.Normal", "<init>:com.yenaly.han1meviewer.VideoCoverSize.Normal", "<init>:com.yenaly.han1meviewer.VideoCoverSize.Simplified", "resizeForVideoCover:com.yenaly.han1meviewer.VideoCoverSize.Simplified", "Simplified:com.yenaly.han1meviewer.VideoCoverSize", "<init>:com.yenaly.han1meviewer.VideoCoverSize"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\Parser.kt": ["comments:com.yenaly.han1meviewer.logic.Parser", "extractTokenFromLoginPage:com.yenaly.han1meviewer.logic.Parser", "Parser:com.yenaly.han1meviewer.logic", "myListItems:com.yenaly.han1meviewer.logic.Parser", "Regex:com.yenaly.han1meviewer.logic.Parser", "viewAndUploadTime:com.yenaly.han1meviewer.logic.Parser.Regex", "subscriptionItems:com.yenaly.han1meviewer.logic.Parser", "hanimeVideoVer2:com.yenaly.han1meviewer.logic.Parser", "<init>:com.yenaly.han1meviewer.logic.Parser.Regex", "hanimeSearch:com.yenaly.han1meviewer.logic.Parser", "commentReply:com.yenaly.han1meviewer.logic.Parser", "videoSource:com.yenaly.han1meviewer.logic.Parser.Regex", "playlists:com.yenaly.han1meviewer.logic.Parser", "homePageVer2:com.yenaly.han1meviewer.logic.Parser", "<init>:com.yenaly.han1meviewer.logic.Parser", "hanimePreview:com.yenaly.han1meviewer.logic.Parser"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\service\\HanimeSubscriptionService.kt": ["subscribeArtist:com.yenaly.han1meviewer.logic.network.service.HanimeSubscriptionService", "HanimeSubscriptionService:com.yenaly.han1meviewer.logic.network.service"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Colors.kt": ["toColorStateList:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\settings\\SharedHKeyframesFragment.kt": ["SharedHKeyframesFragment:com.yenaly.han1meviewer.ui.fragment.settings", "onStart:com.yenaly.han1meviewer.ui.fragment.settings.SharedHKeyframesFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.SharedHKeyframesFragment", "viewModel:com.yenaly.han1meviewer.ui.fragment.settings.SharedHKeyframesFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.settings.SharedHKeyframesFragment", "initData:com.yenaly.han1meviewer.ui.fragment.settings.SharedHKeyframesFragment", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.settings.SharedHKeyframesFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.settings.SharedHKeyframesFragment"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemVideoCommentBinding.java": ["ItemVideoCommentBinding:com.yenaly.han1meviewer.databinding", "inflate:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "ivAvatar:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "bind:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "barrier:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "btnThumbDown:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "tvUsername:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "btnThumbUp:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "linear:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "<init>:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "tvContent:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "tvDate:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "btnReply:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding", "btnViewMoreReplies:com.yenaly.han1meviewer.databinding.ItemVideoCommentBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\HanimePreview.kt": ["videoCode:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "hasNext:com.yenaly.han1meviewer.logic.model.HanimePreview", "headerPicUrl:com.yenaly.han1meviewer.logic.model.HanimePreview", "releaseDate:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "tags:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "title:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "brand:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "relatedPicsUrl:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "videoTitle:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "PreviewInfo:com.yenaly.han1meviewer.logic.model.HanimePreview", "HanimePreview:com.yenaly.han1meviewer.logic.model", "introduction:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "latestHanime:com.yenaly.han1meviewer.logic.model.HanimePreview", "coverUrl:com.yenaly.han1meviewer.logic.model.HanimePreview.PreviewInfo", "previewInfo:com.yenaly.han1meviewer.logic.model.HanimePreview", "hasPrevious:com.yenaly.han1meviewer.logic.model.HanimePreview"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\yenaly\\han1meviewer\\logic\\dao\\HistoryDatabase_Impl.java": ["getSearchHistory:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl", "<init>:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl", "createOpenHelper:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl", "getRequiredAutoMigrationSpecs:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl", "getAutoMigrations:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl", "getWatchHistory:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl", "HistoryDatabase_Impl:com.yenaly.han1meviewer.logic.dao", "getRequiredTypeConverters:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl", "clearAllTables:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl", "createInvalidationTracker:com.yenaly.han1meviewer.logic.dao.HistoryDatabase_Impl"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\activity\\VideoActivity.kt": ["viewModel:com.yenaly.han1meviewer.ui.activity.VideoActivity", "onOrientationChanged:com.yenaly.han1meviewer.ui.activity.VideoActivity", "onDestroy:com.yenaly.han1meviewer.ui.activity.VideoActivity", "setUiStyle:com.yenaly.han1meviewer.ui.activity.VideoActivity", "bindDataObservers:com.yenaly.han1meviewer.ui.activity.VideoActivity", "showRedDotCount:com.yenaly.han1meviewer.ui.activity.VideoActivity", "onFragmentResumedListener:com.yenaly.han1meviewer.ui.activity.VideoActivity", "onStop:com.yenaly.han1meviewer.ui.activity.VideoActivity", "<init>:com.yenaly.han1meviewer.ui.activity.VideoActivity", "getViewBinding:com.yenaly.han1meviewer.ui.activity.VideoActivity", "VideoActivity:com.yenaly.han1meviewer.ui.activity", "initData:com.yenaly.han1meviewer.ui.activity.VideoActivity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\activity\\SettingsActivity.kt": ["viewModel:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "finish:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "currentFragment:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "SettingsActivity:com.yenaly.han1meviewer.ui.activity", "Companion:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "onOptionsItemSelected:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "<init>:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "<init>:com.yenaly.han1meviewer.ui.activity.SettingsActivity.Companion", "onFragmentResumedListener:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "getViewBinding:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "setUiStyle:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "initData:com.yenaly.han1meviewer.ui.activity.SettingsActivity", "H_KEYFRAME_SETTINGS:com.yenaly.han1meviewer.ui.activity.SettingsActivity.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\home\\DownloadFragment.kt": ["DownloadFragment:com.yenaly.han1meviewer.ui.fragment.home", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.home.DownloadFragment", "viewModel:com.yenaly.han1meviewer.ui.fragment.home.DownloadFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.home.DownloadFragment", "initData:com.yenaly.han1meviewer.ui.fragment.home.DownloadFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.home.DownloadFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\activity\\PreviewCommentActivity.kt": ["viewModel:com.yenaly.han1meviewer.ui.activity.PreviewCommentActivity", "getViewBinding:com.yenaly.han1meviewer.ui.activity.PreviewCommentActivity", "initData:com.yenaly.han1meviewer.ui.activity.PreviewCommentActivity", "setUiStyle:com.yenaly.han1meviewer.ui.activity.PreviewCommentActivity", "onOptionsItemSelected:com.yenaly.han1meviewer.ui.activity.PreviewCommentActivity", "<init>:com.yenaly.han1meviewer.ui.activity.PreviewCommentActivity", "PreviewCommentActivity:com.yenaly.han1meviewer.ui.activity", "onDestroy:com.yenaly.han1meviewer.ui.activity.PreviewCommentActivity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\AppViewModel.kt": ["versionFlow:com.yenaly.han1meviewer.ui.viewmodel.AppViewModel", "getLatestVersion:com.yenaly.han1meviewer.ui.viewmodel.AppViewModel", "csrfToken:com.yenaly.han1meviewer.ui.viewmodel.AppViewModel", "AppViewModel:com.yenaly.han1meviewer.ui.viewmodel", "<init>:com.yenaly.han1meviewer.ui.viewmodel.AppViewModel"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\FragmentPageListBinding.java": ["srlPageList:com.yenaly.han1meviewer.databinding.FragmentPageListBinding", "bind:com.yenaly.han1meviewer.databinding.FragmentPageListBinding", "state:com.yenaly.han1meviewer.databinding.FragmentPageListBinding", "toolbar:com.yenaly.han1meviewer.databinding.FragmentPageListBinding", "inflate:com.yenaly.han1meviewer.databinding.FragmentPageListBinding", "coordinator:com.yenaly.han1meviewer.databinding.FragmentPageListBinding", "<init>:com.yenaly.han1meviewer.databinding.FragmentPageListBinding", "FragmentPageListBinding:com.yenaly.han1meviewer.databinding", "rvPageList:com.yenaly.han1meviewer.databinding.FragmentPageListBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HanimeVideoRvAdapter.kt": ["HanimeVideoRvAdapter:com.yenaly.han1meviewer.ui.adapter", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.HanimeVideoRvAdapter.Companion", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeVideoRvAdapter", "getItemViewType:com.yenaly.han1meviewer.ui.adapter.HanimeVideoRvAdapter", "Companion:com.yenaly.han1meviewer.ui.adapter.HanimeVideoRvAdapter", "<init>:com.yenaly.han1meviewer.ui.adapter.HanimeVideoRvAdapter.Companion", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeVideoRvAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\dao\\DownloadDatabase.kt": ["Companion:com.yenaly.han1meviewer.logic.dao.DownloadDatabase", "<init>:com.yenaly.han1meviewer.logic.dao.DownloadDatabase", "<init>:com.yenaly.han1meviewer.logic.dao.DownloadDatabase.Companion", "hanimeDownloadDao:com.yenaly.han1meviewer.logic.dao.DownloadDatabase", "instance:com.yenaly.han1meviewer.logic.dao.DownloadDatabase.Companion", "DownloadDatabase:com.yenaly.han1meviewer.logic.dao"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\Playlists.kt": ["Playlist:com.yenaly.han1meviewer.logic.model.Playlists", "title:com.yenaly.han1meviewer.logic.model.Playlists.Playlist", "ModifiedPlaylistArgs:com.yenaly.han1meviewer.logic.model", "csrfToken:com.yenaly.han1meviewer.logic.model.Playlists", "desc:com.yenaly.han1meviewer.logic.model.ModifiedPlaylistArgs", "Playlists:com.yenaly.han1meviewer.logic.model", "listCode:com.yenaly.han1meviewer.logic.model.Playlists.Playlist", "isDeleted:com.yenaly.han1meviewer.logic.model.ModifiedPlaylistArgs", "playlists:com.yenaly.han1meviewer.logic.model.Playlists", "total:com.yenaly.han1meviewer.logic.model.Playlists.Playlist", "title:com.yenaly.han1meviewer.logic.model.ModifiedPlaylistArgs"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\WatchHistoryRvAdapter.kt": ["WatchHistoryRvAdapter:com.yenaly.han1meviewer.ui.adapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.WatchHistoryRvAdapter", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.WatchHistoryRvAdapter.Companion", "<init>:com.yenaly.han1meviewer.ui.adapter.WatchHistoryRvAdapter", "Companion:com.yenaly.han1meviewer.ui.adapter.WatchHistoryRvAdapter", "<init>:com.yenaly.han1meviewer.ui.adapter.WatchHistoryRvAdapter.Companion", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.WatchHistoryRvAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\entity\\WatchHistoryEntity.kt": ["releaseDate:com.yenaly.han1meviewer.logic.entity.WatchHistoryEntity", "videoCode:com.yenaly.han1meviewer.logic.entity.WatchHistoryEntity", "WatchHistoryEntity:com.yenaly.han1meviewer.logic.entity", "id:com.yenaly.han1meviewer.logic.entity.WatchHistoryEntity", "watchDate:com.yenaly.han1meviewer.logic.entity.WatchHistoryEntity", "coverUrl:com.yenaly.han1meviewer.logic.entity.WatchHistoryEntity", "title:com.yenaly.han1meviewer.logic.entity.WatchHistoryEntity", "releaseDateDays:com.yenaly.han1meviewer.logic.entity.WatchHistoryEntity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\DownloadViewModel.kt": ["downloaded:com.yenaly.han1meviewer.ui.viewmodel.DownloadViewModel", "loadAllDownloadingHanime:com.yenaly.han1meviewer.ui.viewmodel.DownloadViewModel", "loadAllDownloadedHanime:com.yenaly.han1meviewer.ui.viewmodel.DownloadViewModel", "deleteDownloadHanimeBy:com.yenaly.han1meviewer.ui.viewmodel.DownloadViewModel", "DownloadViewModel:com.yenaly.han1meviewer.ui.viewmodel", "currentSortOptionId:com.yenaly.han1meviewer.ui.viewmodel.DownloadViewModel", "updateDownloadHanime:com.yenaly.han1meviewer.ui.viewmodel.DownloadViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\mylist\\FavSubViewModel.kt": ["deleteMyFavVideo:com.yenaly.han1meviewer.ui.viewmodel.mylist.FavSubViewModel", "deleteMyFavVideoFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.FavSubViewModel", "favVideoPage:com.yenaly.han1meviewer.ui.viewmodel.mylist.FavSubViewModel", "getMyFavVideoItems:com.yenaly.han1meviewer.ui.viewmodel.mylist.FavSubViewModel", "favVideoStateFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.FavSubViewModel", "favVideoFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.FavSubViewModel", "clearMyListItems:com.yenaly.han1meviewer.ui.viewmodel.mylist.FavSubViewModel", "FavSubViewModel:com.yenaly.han1meviewer.ui.viewmodel.mylist"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\PopUpFragmentChildCommentBinding.java": ["rvReply:com.yenaly.han1meviewer.databinding.PopUpFragmentChildCommentBinding", "tvChildComment:com.yenaly.han1meviewer.databinding.PopUpFragmentChildCommentBinding", "PopUpFragmentChildCommentBinding:com.yenaly.han1meviewer.databinding", "bind:com.yenaly.han1meviewer.databinding.PopUpFragmentChildCommentBinding", "<init>:com.yenaly.han1meviewer.databinding.PopUpFragmentChildCommentBinding", "inflate:com.yenaly.han1meviewer.databinding.PopUpFragmentChildCommentBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\video\\CommentFragment.kt": ["initData:com.yenaly.han1meviewer.ui.fragment.video.CommentFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.video.CommentFragment", "viewModel:com.yenaly.han1meviewer.ui.fragment.video.CommentFragment", "CommentFragment:com.yenaly.han1meviewer.ui.fragment.video", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.video.CommentFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.video.CommentFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\home\\MyFavVideoFragment.kt": ["viewModel:com.yenaly.han1meviewer.ui.fragment.home.MyFavVideoFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.home.MyFavVideoFragment", "initData:com.yenaly.han1meviewer.ui.fragment.home.MyFavVideoFragment", "onConfigurationChanged:com.yenaly.han1meviewer.ui.fragment.home.MyFavVideoFragment", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.home.MyFavVideoFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.home.MyFavVideoFragment", "MyFavVideoFragment:com.yenaly.han1meviewer.ui.fragment.home", "<init>:com.yenaly.han1meviewer.ui.fragment.home.MyFavVideoFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\HanimeSearchBar.kt": ["ANIM_DURATION:com.yenaly.han1meviewer.ui.view.HanimeSearchBar.Companion", "ss:com.yenaly.han1meviewer.ui.view.HanimeSearchBar.SavedState", "historyAdapter:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "textChangeFlow:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "<init>:com.yenaly.han1meviewer.ui.view.HanimeSearchBar.Companion", "SavedState:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "showHistory:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "canTextChange:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "Companion:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "onTagClickListener:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "isCollapsed:com.yenaly.han1meviewer.ui.view.HanimeSearchBar.SavedState", "HanimeSearchBar:com.yenaly.han1meviewer.ui.view", "onBackClickListener:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "animInterpolator:com.yenaly.han1meviewer.ui.view.HanimeSearchBar.Companion", "searchText:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "onRestoreInstanceState:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "hideHistory:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "onSearchClickListener:com.yenaly.han1meviewer.ui.view.HanimeSearchBar", "onSaveInstanceState:com.yenaly.han1meviewer.ui.view.HanimeSearchBar"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\popup\\ReplyPopup.kt": ["comment:com.yenaly.han1meviewer.ui.popup.ReplyPopup", "initCommentPrefix:com.yenaly.han1meviewer.ui.popup.ReplyPopup", "hint:com.yenaly.han1meviewer.ui.popup.ReplyPopup", "getImplLayoutId:com.yenaly.han1meviewer.ui.popup.ReplyPopup", "onCreate:com.yenaly.han1meviewer.ui.popup.ReplyPopup", "ReplyPopup:com.yenaly.han1meviewer.ui.popup", "setOnSendListener:com.yenaly.han1meviewer.ui.popup.ReplyPopup"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\yenaly\\han1meviewer\\logic\\dao\\DownloadDatabase_Impl.java": ["DownloadDatabase_Impl:com.yenaly.han1meviewer.logic.dao", "clearAllTables:com.yenaly.han1meviewer.logic.dao.DownloadDatabase_Impl", "getRequiredAutoMigrationSpecs:com.yenaly.han1meviewer.logic.dao.DownloadDatabase_Impl", "createOpenHelper:com.yenaly.han1meviewer.logic.dao.DownloadDatabase_Impl", "getRequiredTypeConverters:com.yenaly.han1meviewer.logic.dao.DownloadDatabase_Impl", "createInvalidationTracker:com.yenaly.han1meviewer.logic.dao.DownloadDatabase_Impl", "getHanimeDownloadDao:com.yenaly.han1meviewer.logic.dao.DownloadDatabase_Impl", "getAutoMigrations:com.yenaly.han1meviewer.logic.dao.DownloadDatabase_Impl", "<init>:com.yenaly.han1meviewer.logic.dao.DownloadDatabase_Impl"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemVideoIntroductionBinding.java": ["tvGenre:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "bind:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "btnShare:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "btnSubscribe:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "chineseTitle:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "btnToWebpage:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "uploadTime:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "btnAddToFav:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "inflate:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "tvIntroduction:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "<init>:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "ItemVideoIntroductionBinding:com.yenaly.han1meviewer.databinding", "btnDownload:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "btnMyList:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "views:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "tvArtist:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "ivArtist:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "tags:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "title:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding", "vgArtist:com.yenaly.han1meviewer.databinding.ItemVideoIntroductionBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\RvWrapper.kt": ["<init>:com.yenaly.han1meviewer.ui.adapter.RvWrapper.Companion", "doOnWrap:com.yenaly.han1meviewer.ui.adapter.RvWrapper", "Companion:com.yenaly.han1meviewer.ui.adapter.RvWrapper", "RvWrapper:com.yenaly.han1meviewer.ui.adapter", "wrappedWith:com.yenaly.han1meviewer.ui.adapter.RvWrapper.Companion", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.RvWrapper", "wrapper:com.yenaly.han1meviewer.ui.adapter.RvWrapper", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.RvWrapper"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemHanimePreviewNewsV2Binding.java": ["<init>:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "rvPreview:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "bind:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "tvBrand:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "tvReleaseDate:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "tags:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "tvTitle:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "ItemHanimePreviewNewsV2Binding:com.yenaly.han1meviewer.databinding", "inflate:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "tvVideoTitle:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "tvIntroduction:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding", "ivCoverBig:com.yenaly.han1meviewer.databinding.ItemHanimePreviewNewsV2Binding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HanimeDownloadedRvAdapter.kt": ["onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadedRvAdapter", "Companion:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadedRvAdapter", "HanimeDownloadedRvAdapter:com.yenaly.han1meviewer.ui.adapter", "<init>:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadedRvAdapter.Companion", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadedRvAdapter.Companion", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadedRvAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\HCrashHandler.kt": ["uncaughtException:com.yenaly.han1meviewer.HCrashHandler", "<init>:com.yenaly.han1meviewer.HCrashHandler", "HCrashHandler:com.yenaly.han1meviewer"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\video\\HJzvdStd.kt": ["hKeyframe:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "init:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "onStatePreparingPlaying:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "startProgressTimer:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "clickSpeed:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "touchActionMove:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "showWifiDialog:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "changeUIToPreparingPlaying:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "onLongClick:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "onCompletion:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "speedStringArray:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "setScreenNormal:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "onKeyframeClickListener:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "DEF_SPEED:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "DEF_COUNTDOWN_SEC:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "DEF_SPEED_INDEX:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "videoCode:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "onClick:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "clickHKeyframe:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "getLayoutId:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "setUp:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "gotoNormalScreen:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "DEF_LONG_PRESS_SPEED_TIMES:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "onProgress:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "speedArray:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "<init>:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "HJzvdStd:com.yenaly.han1meviewer.ui.view.video", "onClickUiToggle:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "autoFullscreen:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "onKeyframeLongClickListener:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "Companion:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "DEF_PROGRESS_SLIDE_SENSITIVITY:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "onTouch:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "onStatePreparingChangeUrl:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "onGoHomeClickListener:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "setScreenFullscreen:com.yenaly.han1meviewer.ui.view.video.HJzvdStd", "THRESHOLD:com.yenaly.han1meviewer.ui.view.video.HJzvdStd.Companion", "clickBack:com.yenaly.han1meviewer.ui.view.video.HJzvdStd"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\ParamEnum.kt": ["MyListType:com.yenaly.han1meviewer.logic.model", "value:com.yenaly.han1meviewer.logic.model.CommentPlace", "<init>:com.yenaly.han1meviewer.logic.model.MyListType.FAV_VIDEO", "CHILD_COMMENT:com.yenaly.han1meviewer.logic.model.CommentPlace", "<init>:com.yenaly.han1meviewer.logic.model.CommentPlace.CHILD_COMMENT", "ADD_FAV:com.yenaly.han1meviewer.logic.model.FavStatus", "COMMENT:com.yenaly.han1meviewer.logic.model.CommentPlace", "<init>:com.yenaly.han1meviewer.logic.model.CommentPlace.COMMENT", "FavStatus:com.yenaly.han1meviewer.logic.model", "value:com.yenaly.han1meviewer.logic.model.FavStatus", "value:com.yenaly.han1meviewer.logic.model.MyListType", "FAV_VIDEO:com.yenaly.han1meviewer.logic.model.MyListType", "<init>:com.yenaly.han1meviewer.logic.model.FavStatus.CANCEL_FAV", "SUBSCRIPTION:com.yenaly.han1meviewer.logic.model.MyListType", "CommentPlace:com.yenaly.han1meviewer.logic.model", "CANCEL_FAV:com.yenaly.han1meviewer.logic.model.FavStatus", "<init>:com.yenaly.han1meviewer.logic.model.FavStatus.ADD_FAV", "<init>:com.yenaly.han1meviewer.logic.model.MyListType.WATCH_LATER", "WATCH_LATER:com.yenaly.han1meviewer.logic.model.MyListType", "<init>:com.yenaly.han1meviewer.logic.model.MyListType.SUBSCRIPTION"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\ServiceCreator.kt": ["create:com.yenaly.han1meviewer.logic.network.ServiceCreator", "updateUserAgent:com.yenaly.han1meviewer.logic.network.ServiceCreator", "ensureInitialized:com.yenaly.han1meviewer.logic.network.ServiceCreator", "githubClient:com.yenaly.han1meviewer.logic.network.ServiceCreator", "initialize:com.yenaly.han1meviewer.logic.network.ServiceCreator", "okHttpClient:com.yenaly.han1meviewer.logic.network.ServiceCreator", "<init>:com.yenaly.han1meviewer.logic.network.ServiceCreator", "ServiceCreator:com.yenaly.han1meviewer.logic.network", "cache:com.yenaly.han1meviewer.logic.network.ServiceCreator", "createGitHubApi:com.yenaly.han1meviewer.logic.network.ServiceCreator", "rebuildOkHttpClient:com.yenaly.han1meviewer.logic.network.ServiceCreator"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemWatchHistoryBinding.java": ["tvTitle:com.yenaly.han1meviewer.databinding.ItemWatchHistoryBinding", "bind:com.yenaly.han1meviewer.databinding.ItemWatchHistoryBinding", "<init>:com.yenaly.han1meviewer.databinding.ItemWatchHistoryBinding", "ivCover:com.yenaly.han1meviewer.databinding.ItemWatchHistoryBinding", "ItemWatchHistoryBinding:com.yenaly.han1meviewer.databinding", "tvAddedTime:com.yenaly.han1meviewer.databinding.ItemWatchHistoryBinding", "inflate:com.yenaly.han1meviewer.databinding.ItemWatchHistoryBinding", "tvReleaseDate:com.yenaly.han1meviewer.databinding.ItemWatchHistoryBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\HOptionChip.kt": ["ss:com.yenaly.han1meviewer.ui.view.HOptionChip.SavedState", "isAvailable:com.yenaly.han1meviewer.ui.view.HOptionChip", "SavedState:com.yenaly.han1meviewer.ui.view.HOptionChip", "isChecked:com.yenaly.han1meviewer.ui.view.HOptionChip.SavedState", "isChecked:com.yenaly.han1meviewer.ui.view.HOptionChip", "onRestoreInstanceState:com.yenaly.han1meviewer.ui.view.HOptionChip", "toggle:com.yenaly.han1meviewer.ui.view.HOptionChip", "HOptionChip:com.yenaly.han1meviewer.ui.view", "setChecked:com.yenaly.han1meviewer.ui.view.HOptionChip", "onSaveInstanceState:com.yenaly.han1meviewer.ui.view.HOptionChip"], "src\\main\\java\\com\\yenaly\\han1meviewer\\worker\\WorkerMixin.kt": ["inputData:com.yenaly.han1meviewer.worker.WorkerMixin", "WorkerMixin:com.yenaly.han1meviewer.worker"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HanimeSearchHistoryRvAdapter.kt": ["<init>:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter.Companion", "OnItemViewClickListener:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter", "onItemClickListener:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter.OnItemViewClickListener", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter.Companion", "<init>:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter", "HanimeSearchHistoryRvAdapter:com.yenaly.han1meviewer.ui.adapter", "Companion:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter", "listener:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter", "onItemRemoveListener:com.yenaly.han1meviewer.ui.adapter.HanimeSearchHistoryRvAdapter.OnItemViewClickListener"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\BaseColumnTitleBinding.java": ["subTitle:com.yenaly.han1meviewer.databinding.BaseColumnTitleBinding", "title:com.yenaly.han1meviewer.databinding.BaseColumnTitleBinding", "bind:com.yenaly.han1meviewer.databinding.BaseColumnTitleBinding", "BaseColumnTitleBinding:com.yenaly.han1meviewer.databinding", "inflate:com.yenaly.han1meviewer.databinding.BaseColumnTitleBinding", "<init>:com.yenaly.han1meviewer.databinding.BaseColumnTitleBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\service\\HGitHubService.kt": ["getArtifacts:com.yenaly.han1meviewer.logic.network.service.HGitHubService", "HGitHubService:com.yenaly.han1meviewer.logic.network.service", "getWorkflowRuns:com.yenaly.han1meviewer.logic.network.service.HGitHubService", "request:com.yenaly.han1meviewer.logic.network.service.HGitHubService", "getLatestVersion:com.yenaly.han1meviewer.logic.network.service.HGitHubService", "getCommitComparison:com.yenaly.han1meviewer.logic.network.service.HGitHubService"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\Subscription.kt": ["isCheckBoxVisible:com.yenaly.han1meviewer.logic.model.Subscription", "Subscription:com.yenaly.han1meviewer.logic.model", "artistId:com.yenaly.han1meviewer.logic.model.Subscription", "isDeleteVisible:com.yenaly.han1meviewer.logic.model.Subscription", "avatarUrl:com.yenaly.han1meviewer.logic.model.Subscription", "name:com.yenaly.han1meviewer.logic.model.Subscription"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ActivityPreviewCommentBinding.java": ["<init>:com.yenaly.han1meviewer.databinding.ActivityPreviewCommentBinding", "bind:com.yenaly.han1meviewer.databinding.ActivityPreviewCommentBinding", "toolbar:com.yenaly.han1meviewer.databinding.ActivityPreviewCommentBinding", "ActivityPreviewCommentBinding:com.yenaly.han1meviewer.databinding", "fcvPreComment:com.yenaly.han1meviewer.databinding.ActivityPreviewCommentBinding", "inflate:com.yenaly.han1meviewer.databinding.ActivityPreviewCommentBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\PreviewCommentPrefetcher.kt": ["Scope:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher", "here:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher.Companion", "<init>:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher.Scope", "commentFlow:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher", "<init>:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher", "PREVIEW_COMMENT_ACTIVITY:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher.Scope.Companion", "Companion:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher.Scope", "<init>:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher.Companion", "fetch:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher", "PREVIEW_ACTIVITY:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher.Scope.Companion", "Companion:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher", "tag:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher", "update:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher", "<init>:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher.Scope.Companion", "PreviewCommentPrefetcher:com.yenaly.han1meviewer.ui.viewmodel", "bye:com.yenaly.han1meviewer.ui.viewmodel.PreviewCommentPrefetcher.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\search\\SearchOptionsPopupFragment.kt": ["myListViewModel:com.yenaly.han1meviewer.ui.fragment.search.SearchOptionsPopupFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.search.SearchOptionsPopupFragment", "initData:com.yenaly.han1meviewer.ui.fragment.search.SearchOptionsPopupFragment", "SearchOptionsPopupFragment:com.yenaly.han1meviewer.ui.fragment.search", "Tags:com.yenaly.han1meviewer.ui.fragment.search.SearchOptionsPopupFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.search.SearchOptionsPopupFragment.Tags", "<init>:com.yenaly.han1meviewer.ui.fragment.search.SearchOptionsPopupFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\MultiItemEntity.kt": ["itemType:com.yenaly.han1meviewer.logic.model.MultiItemEntity", "MultiItemEntity:com.yenaly.han1meviewer.logic.model"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\VideoViewModel.kt": ["observeKeyframe:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "Companion:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "removeFromFavVideo:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "<init>:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel.Companion", "unsubscribeArtist:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "hanimeVideoStateFlow:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "VideoViewModel:com.yenaly.han1meviewer.ui.viewmodel", "findDownloadedHanime:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "addToFavVideo:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "getHanimeVideo:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "removeHKeyframe:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "modifyMyListFlow:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "MIN_H_KEYFRAME_SAVE_INTERVAL:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel.Companion", "modifyMyList:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "hanimeVideoFlow:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "addToFavVideoFlow:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "modifyHKeyframeFlow:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "loadDownloadedFlow:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "hKeyframes:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "appendHKeyframe:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "insertWatchHistory:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "subscribeArtist:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "modifyHKeyframe:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "videoCode:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel", "subscribeArtistFlow:com.yenaly.han1meviewer.ui.viewmodel.VideoViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\CollapsibleTags.kt": ["<init>:com.yenaly.han1meviewer.ui.view.CollapsibleTags.Companion", "isCollapsedEnabled:com.yenaly.han1meviewer.ui.view.CollapsibleTags", "onRestoreInstanceState:com.yenaly.han1meviewer.ui.view.CollapsibleTags", "tags:com.yenaly.han1meviewer.ui.view.CollapsibleTags", "onSaveInstanceState:com.yenaly.han1meviewer.ui.view.CollapsibleTags", "isCollapsedEnabled:com.yenaly.han1meviewer.ui.view.CollapsibleTags.SavedState", "lifecycle:com.yenaly.han1meviewer.ui.view.CollapsibleTags", "ANIM_DURATION:com.yenaly.han1meviewer.ui.view.CollapsibleTags.Companion", "SavedState:com.yenaly.han1meviewer.ui.view.CollapsibleTags", "ss:com.yenaly.han1meviewer.ui.view.CollapsibleTags.SavedState", "CollapsibleTags:com.yenaly.han1meviewer.ui.view", "animInterpolator:com.yenaly.han1meviewer.ui.view.CollapsibleTags.Companion", "isCollapsed:com.yenaly.han1meviewer.ui.view.CollapsibleTags", "isCollapsed:com.yenaly.han1meviewer.ui.view.CollapsibleTags.SavedState", "tags:com.yenaly.han1meviewer.ui.view.CollapsibleTags.SavedState", "Companion:com.yenaly.han1meviewer.ui.view.CollapsibleTags", "chipGroupMeasureHeight:com.yenaly.han1meviewer.ui.view.CollapsibleTags.SavedState"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\IToolbarFragment.kt": ["setupToolbar:com.yenaly.han1meviewer.ui.fragment.IToolbarFragment", "IToolbarFragment:com.yenaly.han1meviewer.ui.fragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HanimePreviewTourRvAdapter.kt": ["onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimePreviewTourRvAdapter", "<init>:com.yenaly.han1meviewer.ui.adapter.HanimePreviewTourRvAdapter", "HanimePreviewTourRvAdapter:com.yenaly.han1meviewer.ui.adapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimePreviewTourRvAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\GitHubDns.kt": ["<init>:com.yenaly.han1meviewer.logic.network.GitHubDns", "lookup:com.yenaly.han1meviewer.logic.network.GitHubDns", "GitHubDns:com.yenaly.han1meviewer.logic.network"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\dao\\MiscellanyDatabase.kt": ["Companion:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase", "MiscellanyDatabase:com.yenaly.han1meviewer.logic.dao", "instance:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase.Companion", "hKeyframeDao:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase", "<init>:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase", "<init>:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\entity\\SearchHistoryEntity.kt": ["id:com.yenaly.han1meviewer.logic.entity.SearchHistoryEntity", "SearchHistoryEntity:com.yenaly.han1meviewer.logic.entity", "query:com.yenaly.han1meviewer.logic.entity.SearchHistoryEntity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\TextViews.kt": ["setResizableText:com.yenaly.han1meviewer.util", "updateDrawState:com.yenaly.han1meviewer.util.NoUnderlineClickSpan", "context:com.yenaly.han1meviewer.util.NoUnderlineClickSpan"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\service\\HanimeBaseService.kt": ["getHanimeVideo:com.yenaly.han1meviewer.logic.network.service.HanimeBaseService", "getLoginPage:com.yenaly.han1meviewer.logic.network.service.HanimeBaseService", "login:com.yenaly.han1meviewer.logic.network.service.HanimeBaseService", "HanimeBaseService:com.yenaly.han1meviewer.logic.network.service", "getHanimeSearchResult:com.yenaly.han1meviewer.logic.network.service.HanimeBaseService", "getHomePage:com.yenaly.han1meviewer.logic.network.service.HanimeBaseService", "getHanimePreview:com.yenaly.han1meviewer.logic.network.service.HanimeBaseService"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Versions.kt": ["updateFile:com.yenaly.han1meviewer.util", "checkNeedUpdate:com.yenaly.han1meviewer.util", "installApkPackage:com.yenaly.han1meviewer.util", "showUpdateDialog:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\settings\\NetworkSettingsFragment.kt": ["show:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment.ProxyDialog", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment.Companion", "USE_BUILT_IN_HOSTS:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment.Companion", "PROXY_PORT:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment.Companion", "NetworkSettingsFragment:com.yenaly.han1meviewer.ui.fragment.settings", "PROXY_TYPE:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment.Companion", "onStart:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment", "onPreferencesCreated:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment", "PROXY:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment.Companion", "PROXY_IP:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment.Companion", "ProxyDialog:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment", "Companion:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment", "DOMAIN_NAME:com.yenaly.han1meviewer.ui.fragment.settings.NetworkSettingsFragment.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\github\\Release.kt": ["nodeID:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "starredURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "uploader:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "publishedAt:com.yenaly.han1meviewer.logic.model.github.Release", "createdAt:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "subscriptionsURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "url:com.yenaly.han1meviewer.logic.model.github.Release.Author", "Asset:com.yenaly.han1meviewer.logic.model.github.Release", "htmlURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "prerelease:com.yenaly.han1meviewer.logic.model.github.Release", "url:com.yenaly.han1meviewer.logic.model.github.Release", "updatedAt:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "browserDownloadURL:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "siteAdmin:com.yenaly.han1meviewer.logic.model.github.Release.Author", "Release:com.yenaly.han1meviewer.logic.model.github", "uploadURL:com.yenaly.han1meviewer.logic.model.github.Release", "name:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "zipballURL:com.yenaly.han1meviewer.logic.model.github.Release", "avatarURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "eventsURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "receivedEventsURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "gistsURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "contentType:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "name:com.yenaly.han1meviewer.logic.model.github.Release", "login:com.yenaly.han1meviewer.logic.model.github.Release.Author", "assetsURL:com.yenaly.han1meviewer.logic.model.github.Release", "reposURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "id:com.yenaly.han1meviewer.logic.model.github.Release.Author", "nodeID:com.yenaly.han1meviewer.logic.model.github.Release", "label:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "targetCommitish:com.yenaly.han1meviewer.logic.model.github.Release", "state:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "draft:com.yenaly.han1meviewer.logic.model.github.Release", "tarballURL:com.yenaly.han1meviewer.logic.model.github.Release", "url:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "id:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "assets:com.yenaly.han1meviewer.logic.model.github.Release", "followingURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "Author:com.yenaly.han1meviewer.logic.model.github.Release", "htmlURL:com.yenaly.han1meviewer.logic.model.github.Release", "gravatarID:com.yenaly.han1meviewer.logic.model.github.Release.Author", "downloadCount:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "type:com.yenaly.han1meviewer.logic.model.github.Release.Author", "body:com.yenaly.han1meviewer.logic.model.github.Release", "size:com.yenaly.han1meviewer.logic.model.github.Release.Asset", "tagName:com.yenaly.han1meviewer.logic.model.github.Release", "organizationsURL:com.yenaly.han1meviewer.logic.model.github.Release.Author", "author:com.yenaly.han1meviewer.logic.model.github.Release", "nodeID:com.yenaly.han1meviewer.logic.model.github.Release.Author", "id:com.yenaly.han1meviewer.logic.model.github.Release", "createdAt:com.yenaly.han1meviewer.logic.model.github.Release", "followersURL:com.yenaly.han1meviewer.logic.model.github.Release.Author"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\HUpdater.kt": ["HUpdater:com.yenaly.han1meviewer.logic.network", "DEFAULT_BRANCH:com.yenaly.han1meviewer.logic.network.HUpdater", "checkForUpdate:com.yenaly.han1meviewer.logic.network.HUpdater", "TAG:com.yenaly.han1meviewer.logic.network.HUpdater", "injectUpdate:com.yenaly.han1meviewer.logic.network.HUpdater", "<init>:com.yenaly.han1meviewer.logic.network.HUpdater"], "src\\main\\java\\com\\yenaly\\han1meviewer\\Preferences.kt": ["<init>:com.yenaly.han1meviewer.Preferences", "proxyIp:com.yenaly.han1meviewer.Preferences", "isUpdateDialogVisible:com.yenaly.han1meviewer.Preferences", "slideSensitivity:com.yenaly.han1meviewer.Preferences", "proxyType:com.yenaly.han1meviewer.Preferences", "updateNodeId:com.yenaly.han1meviewer.Preferences", "showCommentWhenCountdown:com.yenaly.han1meviewer.Preferences", "updatePopupIntervalDays:com.yenaly.han1meviewer.Preferences", "hKeyframesEnable:com.yenaly.han1meviewer.Preferences", "sharedHKeyframesEnable:com.yenaly.han1meviewer.Preferences", "loginCookie:com.yenaly.han1meviewer.Preferences", "isAlreadyLogin:com.yenaly.han1meviewer.Preferences", "videoLanguage:com.yenaly.han1meviewer.Preferences", "showBottomProgress:com.yenaly.han1meviewer.Preferences", "switchPlayerKernel:com.yenaly.han1meviewer.Preferences", "preferenceSp:com.yenaly.han1meviewer.Preferences", "loginCookieStateFlow:com.yenaly.han1meviewer.Preferences", "whenCountdownRemind:com.yenaly.han1meviewer.Preferences", "lastUpdatePopupTime:com.yenaly.han1meviewer.Preferences", "longPressSpeedTime:com.yenaly.han1meviewer.Preferences", "loginStateFlow:com.yenaly.han1meviewer.Preferences", "baseUrl:com.yenaly.han1meviewer.Preferences", "playerSpeed:com.yenaly.han1meviewer.Preferences", "useBuiltInHosts:com.yenaly.han1meviewer.Preferences", "proxyPort:com.yenaly.han1meviewer.Preferences", "isAnalyticsEnabled:com.yenaly.han1meviewer.Preferences", "Preferences:com.yenaly.han1meviewer", "useCIUpdateChannel:com.yenaly.han1meviewer.Preferences", "sharedHKeyframesUseFirst:com.yenaly.han1meviewer.Preferences"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\MyListItems.kt": ["MyListItems:com.yenaly.han1meviewer.logic.model", "csrfToken:com.yenaly.han1meviewer.logic.model.MyListItems", "desc:com.yenaly.han1meviewer.logic.model.MyListItems", "hanimeInfo:com.yenaly.han1meviewer.logic.model.MyListItems"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\SettingsViewModel.kt": ["modifyHKeyframe:com.yenaly.han1meviewer.ui.viewmodel.SettingsViewModel", "insertHKeyframes:com.yenaly.han1meviewer.ui.viewmodel.SettingsViewModel", "loadAllSharedHKeyframes:com.yenaly.han1meviewer.ui.viewmodel.SettingsViewModel", "updateHKeyframes:com.yenaly.han1meviewer.ui.viewmodel.SettingsViewModel", "loadAllHKeyframes:com.yenaly.han1meviewer.ui.viewmodel.SettingsViewModel", "removeHKeyframe:com.yenaly.han1meviewer.ui.viewmodel.SettingsViewModel", "SettingsViewModel:com.yenaly.han1meviewer.ui.viewmodel", "deleteHKeyframes:com.yenaly.han1meviewer.ui.viewmodel.SettingsViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\activity\\MainActivity.kt": ["bindDataObservers:com.yenaly.han1meviewer.ui.activity.MainActivity", "onFragmentResumedListener:com.yenaly.han1meviewer.ui.activity.MainActivity", "setUiStyle:com.yenaly.han1meviewer.ui.activity.MainActivity", "onDrawerClosed:com.yenaly.han1meviewer.ui.activity.MainActivity", "setupWithMainNavController:com.yenaly.han1meviewer.ui.activity.MainActivity", "onSupportNavigateUp:com.yenaly.han1meviewer.ui.activity.MainActivity", "viewModel:com.yenaly.han1meviewer.ui.activity.MainActivity", "navController:com.yenaly.han1meviewer.ui.activity.MainActivity", "currentFragment:com.yenaly.han1meviewer.ui.activity.MainActivity", "<init>:com.yenaly.han1meviewer.ui.activity.MainActivity", "MainActivity:com.yenaly.han1meviewer.ui.activity", "onDrawerOpened:com.yenaly.han1meviewer.ui.activity.MainActivity", "onDrawerStateChanged:com.yenaly.han1meviewer.ui.activity.MainActivity", "getViewBinding:com.yenaly.han1meviewer.ui.activity.MainActivity", "initData:com.yenaly.han1meviewer.ui.activity.MainActivity", "navHostFragment:com.yenaly.han1meviewer.ui.activity.MainActivity", "onStart:com.yenaly.han1meviewer.ui.activity.MainActivity", "onDrawerSlide:com.yenaly.han1meviewer.ui.activity.MainActivity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\VideoCommentRvAdapter.kt": ["VideoCommentRvAdapter:com.yenaly.han1meviewer.ui.adapter", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.VideoCommentRvAdapter.Companion", "Companion:com.yenaly.han1meviewer.ui.adapter.VideoCommentRvAdapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.VideoCommentRvAdapter", "replyPopup:com.yenaly.han1meviewer.ui.adapter.VideoCommentRvAdapter", "<init>:com.yenaly.han1meviewer.ui.adapter.VideoCommentRvAdapter.Companion", "submitList:com.yenaly.han1meviewer.ui.adapter.VideoCommentRvAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.VideoCommentRvAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\HanimeResolution.kt": ["RES_480P:com.yenaly.han1meviewer.HanimeResolution.Companion", "RES_240P:com.yenaly.han1meviewer.HanimeResolution.Companion", "RES_720P:com.yenaly.han1meviewer.HanimeResolution.Companion", "toResolutionLinkMap:com.yenaly.han1meviewer.HanimeResolution", "HanimeLink:com.yenaly.han1meviewer", "<init>:com.yenaly.han1meviewer.HanimeResolution.Companion", "<init>:com.yenaly.han1meviewer.HanimeResolution", "Companion:com.yenaly.han1meviewer.HanimeResolution", "link:com.yenaly.han1meviewer.HanimeLink", "HanimeResolution:com.yenaly.han1meviewer", "type:com.yenaly.han1meviewer.HanimeLink", "RES_1080P:com.yenaly.han1meviewer.HanimeResolution.Companion", "RES_UNKNOWN:com.yenaly.han1meviewer.HanimeResolution.Companion", "suffix:com.yenaly.han1meviewer.HanimeLink", "parseResolution:com.yenaly.han1meviewer.HanimeResolution", "ResolutionLinkMap:com.yenaly.han1meviewer"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Cookies.kt": ["cookie:com.yenaly.han1meviewer.util.CookieString", "toLoginCookieList:com.yenaly.han1meviewer.util", "CookieString:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\github\\Latest.kt": ["nodeId:com.yenaly.han1meviewer.logic.model.github.Latest", "version:com.yenaly.han1meviewer.logic.model.github.Latest", "Latest:com.yenaly.han1meviewer.logic.model.github", "downloadLink:com.yenaly.han1meviewer.logic.model.github.Latest", "changelog:com.yenaly.han1meviewer.logic.model.github.Latest"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\VideoSpeedAdapter.kt": ["VideoSpeedAdapter:com.yenaly.han1meviewer.ui.adapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.VideoSpeedAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.VideoSpeedAdapter"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ActivityLoginBinding.java": ["ActivityLoginBinding:com.yenaly.han1meviewer.databinding", "<init>:com.yenaly.han1meviewer.databinding.ActivityLoginBinding", "inflate:com.yenaly.han1meviewer.databinding.ActivityLoginBinding", "wvLogin:com.yenaly.han1meviewer.databinding.ActivityLoginBinding", "toolbar:com.yenaly.han1meviewer.databinding.ActivityLoginBinding", "bind:com.yenaly.han1meviewer.databinding.ActivityLoginBinding", "srlLogin:com.yenaly.han1meviewer.databinding.ActivityLoginBinding"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\PopUpFragmentSearchOptionsBinding.java": ["duration:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "tvSubscription:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "brand:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "tag:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "llSubscription:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "bind:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "type:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "releaseDate:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "sortOption:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "PopUpFragmentSearchOptionsBinding:com.yenaly.han1meviewer.databinding", "rvSubscription:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "<init>:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding", "inflate:com.yenaly.han1meviewer.databinding.PopUpFragmentSearchOptionsBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\worker\\HanimeDownloadWorker.kt": ["PROGRESS:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "Companion:com.yenaly.han1meviewer.worker.HanimeDownloadWorker", "COVER_URL:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "doWork:com.yenaly.han1meviewer.worker.HanimeDownloadWorker", "HANIME_NAME:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "collectOutput:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "<init>:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "VIDEO_TYPE:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "BACKOFF_DELAY:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "VIDEO_CODE:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "HanimeDownloadWorker:com.yenaly.han1meviewer.worker", "REDOWNLOAD:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "FAILED_REASON:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "DOWNLOAD_URL:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "QUALITY:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "build:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "RESPONSE_INTERVAL:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "TAG:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion", "DELETE:com.yenaly.han1meviewer.worker.HanimeDownloadWorker.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\dao\\HistoryDatabase.kt": ["HistoryDatabase:com.yenaly.han1meviewer.logic.dao", "<init>:com.yenaly.han1meviewer.logic.dao.HistoryDatabase.Companion", "Companion:com.yenaly.han1meviewer.logic.dao.HistoryDatabase", "<init>:com.yenaly.han1meviewer.logic.dao.HistoryDatabase", "searchHistory:com.yenaly.han1meviewer.logic.dao.HistoryDatabase", "watchHistory:com.yenaly.han1meviewer.logic.dao.HistoryDatabase", "instance:com.yenaly.han1meviewer.logic.dao.HistoryDatabase.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\dao\\HanimeDownloadDao.kt": ["loadAllDownloadedHanimeById:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "insert:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "deleteBy:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "loadAllDownloadingHanime:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "loadAllDownloadedHanime:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "findBy:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "delete:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "<init>:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "update:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "loadAllDownloadedHanimeByTitle:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "pauseAll:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao", "HanimeDownloadDao:com.yenaly.han1meviewer.logic.dao", "isExist:com.yenaly.han1meviewer.logic.dao.HanimeDownloadDao"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemHanimeVideoBinding.java": ["cover:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "duration:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "inflate:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "coverWrapper:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "iconViews:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "views:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "linear:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "<init>:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "time:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "title:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "frame:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "genreAndUploader:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "isPlaying:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "bind:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding", "ItemHanimeVideoBinding:com.yenaly.han1meviewer.databinding", "iconTime:com.yenaly.han1meviewer.databinding.ItemHanimeVideoBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\yenaly\\han1meviewer\\logic\\dao\\WatchHistoryDao_Impl.java": ["insert:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao_Impl", "WatchHistoryDao_Impl:com.yenaly.han1meviewer.logic.dao", "find:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao_Impl", "getRequiredConverters:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao_Impl", "<init>:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao_Impl", "insertOrUpdate:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao_Impl", "delete:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao_Impl", "deleteAll:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao_Impl", "loadAll:com.yenaly.han1meviewer.logic.dao.WatchHistoryDao_Impl"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\SearchOption.kt": ["flatten:com.yenaly.han1meviewer.logic.model.SearchOption.Companion", "hashCode:com.yenaly.han1meviewer.logic.model.SearchOption", "searchKey:com.yenaly.han1meviewer.logic.model.SearchOption", "Companion:com.yenaly.han1meviewer.logic.model.SearchOption", "en:com.yenaly.han1meviewer.logic.model.SearchOption.Language", "lang:com.yenaly.han1meviewer.logic.model.SearchOption", "zhrCN:com.yenaly.han1meviewer.logic.model.SearchOption.Language", "SearchOption:com.yenaly.han1meviewer.logic.model", "get:com.yenaly.han1meviewer.logic.model.SearchOption.Companion", "toScopeKey:com.yenaly.han1meviewer.logic.model.SearchOption.Companion", "name:com.yenaly.han1meviewer.logic.model.SearchOption", "<init>:com.yenaly.han1meviewer.logic.model.SearchOption.Companion", "Language:com.yenaly.han1meviewer.logic.model.SearchOption", "zhrTW:com.yenaly.han1meviewer.logic.model.SearchOption.Language", "value:com.yenaly.han1meviewer.logic.model.SearchOption"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\state\\DownloadState.kt": ["None:com.yenaly.han1meviewer.logic.state.DownloadState", "throwable:com.yenaly.han1meviewer.logic.state.DownloadState.Error", "Done:com.yenaly.han1meviewer.logic.state.DownloadState", "<init>:com.yenaly.han1meviewer.logic.state.DownloadState", "DownloadState:com.yenaly.han1meviewer.logic.state", "file:com.yenaly.han1meviewer.logic.state.DownloadState.Done", "Progress:com.yenaly.han1meviewer.logic.state.DownloadState", "Error:com.yenaly.han1meviewer.logic.state.DownloadState", "value:com.yenaly.han1meviewer.logic.state.DownloadState.Progress", "<init>:com.yenaly.han1meviewer.logic.state.DownloadState.None"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\StateLayoutMixin.kt": ["init:com.yenaly.han1meviewer.ui.StateLayoutMixin", "StateLayoutMixin:com.yenaly.han1meviewer.ui"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\home\\download\\DownloadingFragment.kt": ["<init>:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadingFragment", "initData:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadingFragment", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadingFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadingFragment", "viewModel:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadingFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.home.download.DownloadingFragment", "DownloadingFragment:com.yenaly.han1meviewer.ui.fragment.home.download"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemHanimeVideoSimplifiedBinding.java": ["frame:com.yenaly.han1meviewer.databinding.ItemHanimeVideoSimplifiedBinding", "linear:com.yenaly.han1meviewer.databinding.ItemHanimeVideoSimplifiedBinding", "coverWrapper:com.yenaly.han1meviewer.databinding.ItemHanimeVideoSimplifiedBinding", "bind:com.yenaly.han1meviewer.databinding.ItemHanimeVideoSimplifiedBinding", "ItemHanimeVideoSimplifiedBinding:com.yenaly.han1meviewer.databinding", "title:com.yenaly.han1meviewer.databinding.ItemHanimeVideoSimplifiedBinding", "cover:com.yenaly.han1meviewer.databinding.ItemHanimeVideoSimplifiedBinding", "inflate:com.yenaly.han1meviewer.databinding.ItemHanimeVideoSimplifiedBinding", "<init>:com.yenaly.han1meviewer.databinding.ItemHanimeVideoSimplifiedBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HanimePreviewNewsRvAdapter.kt": ["<init>:com.yenaly.han1meviewer.ui.adapter.HanimePreviewNewsRvAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimePreviewNewsRvAdapter.PreviewPicRvAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimePreviewNewsRvAdapter", "HanimePreviewNewsRvAdapter:com.yenaly.han1meviewer.ui.adapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimePreviewNewsRvAdapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimePreviewNewsRvAdapter.PreviewPicRvAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\HanimeNetwork.kt": ["HanimeNetwork:com.yenaly.han1meviewer.logic.network", "githubService:com.yenaly.han1meviewer.logic.network.HanimeNetwork", "subscriptionService:com.yenaly.han1meviewer.logic.network.HanimeNetwork", "rebuildNetwork:com.yenaly.han1meviewer.logic.network.HanimeNetwork", "commentService:com.yenaly.han1meviewer.logic.network.HanimeNetwork", "<init>:com.yenaly.han1meviewer.logic.network.HanimeNetwork", "hanimeService:com.yenaly.han1meviewer.logic.network.HanimeNetwork", "myListService:com.yenaly.han1meviewer.logic.network.HanimeNetwork"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\exception\\ParseException.kt": ["ParseException:com.yenaly.han1meviewer.logic.exception"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ItemHanimeDownloadedBinding.java": ["inflate:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "ivCover:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "tvQuality:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "btnLocalPlayback:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "tvReleaseDate:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "Barrier:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "tvAddedTime:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "<init>:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "tvTitle:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "btnDelete:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "bind:com.yenaly.han1meviewer.databinding.ItemHanimeDownloadedBinding", "ItemHanimeDownloadedBinding:com.yenaly.han1meviewer.databinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\activity\\PreviewActivity.kt": ["onOptionsItemSelected:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "onDestroy:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "Companion:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "FORMATTED_FORMAT:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils.Companion", "<init>:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "PreviewActivity:com.yenaly.han1meviewer.ui.activity", "<init>:com.yenaly.han1meviewer.ui.activity.PreviewActivity.Companion", "onCreateOptionsMenu:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "toPrevDate:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils", "initData:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "getViewBinding:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "nextDate:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils", "Companion:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils", "currentDate:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils", "bindDataObservers:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "<init>:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils", "setUiStyle:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "current:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils", "prevDate:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils", "<init>:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils.Companion", "NORMAL_FORMAT:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils.Companion", "viewModel:com.yenaly.han1meviewer.ui.activity.PreviewActivity", "toNextDate:com.yenaly.han1meviewer.ui.activity.PreviewActivity.DateUtils"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\exception\\IPBlockedException.kt": ["IPBlockedException:com.yenaly.han1meviewer.logic.exception"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\FragmentPlaylistBinding.java": ["appBar:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "nvPlaylist:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "<init>:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "rvPageList:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "playlistHeader:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "btnNewPlaylist:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "inflate:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "cover:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "toolbar:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "btnRefreshPlaylists:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "rvPlaylist:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "coordinator:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "dlPlaylist:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "statePlaylist:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "srlPageList:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "FragmentPlaylistBinding:com.yenaly.han1meviewer.databinding", "statePageList:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding", "bind:com.yenaly.han1meviewer.databinding.FragmentPlaylistBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\search\\HMultiChoicesDialog.kt": ["adapterMap:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "loadSavedTags:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "HMultiChoicesDialog:com.yenaly.han1meviewer.ui.fragment.search", "addTagScope:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "show:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "adapterMap:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog.Companion", "collectCheckedTags:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "context:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "clearAllChecks:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "UNKNOWN_ADAPTER:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog.Companion", "setOnSaveListener:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "setOnResetListener:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "setOnDismissListener:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog", "<init>:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog.Companion", "Companion:com.yenaly.han1meviewer.ui.fragment.search.HMultiChoicesDialog"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\video\\VideoIntroductionFragment.kt": ["binding:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.VideoIntroductionAdapter", "COMPARATOR:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.Companion", "onInterceptTouchEvent:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.VideoIntroTouchListener", "<init>:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.VideoIntroTouchListener", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment", "initData:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.Companion", "<init>:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.VideoIntroductionAdapter", "onRequestDisallowInterceptTouchEvent:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.VideoIntroTouchListener", "VideoIntroductionFragment:com.yenaly.han1meviewer.ui.fragment.video", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment", "onCreateViewHolder:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.VideoIntroductionAdapter", "Companion:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment", "onTouchEvent:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.VideoIntroTouchListener", "viewModel:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment", "onBindViewHolder:com.yenaly.han1meviewer.ui.fragment.video.VideoIntroductionFragment.VideoIntroductionAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\HanimeInfo.kt": ["duration:com.yenaly.han1meviewer.logic.model.HanimeInfo", "uploadTime:com.yenaly.han1meviewer.logic.model.HanimeInfo", "coverUrl:com.yenaly.han1meviewer.logic.model.HanimeInfo", "views:com.yenaly.han1meviewer.logic.model.HanimeInfo", "HanimeInfo:com.yenaly.han1meviewer.logic.model", "SIMPLIFIED:com.yenaly.han1meviewer.logic.model.HanimeInfo.Companion", "videoCode:com.yenaly.han1meviewer.logic.model.HanimeInfo", "uploader:com.yenaly.han1meviewer.logic.model.HanimeInfo", "genre:com.yenaly.han1meviewer.logic.model.HanimeInfo", "HanimeInfoType:com.yenaly.han1meviewer.logic.model", "NORMAL:com.yenaly.han1meviewer.logic.model.HanimeInfo.Companion", "itemType:com.yenaly.han1meviewer.logic.model.HanimeInfo", "<init>:com.yenaly.han1meviewer.logic.model.HanimeInfo.Companion", "title:com.yenaly.han1meviewer.logic.model.HanimeInfo", "isPlaying:com.yenaly.han1meviewer.logic.model.HanimeInfo", "Companion:com.yenaly.han1meviewer.logic.model.HanimeInfo"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\home\\MyWatchLaterFragment.kt": ["MyWatchLaterFragment:com.yenaly.han1meviewer.ui.fragment.home", "<init>:com.yenaly.han1meviewer.ui.fragment.home.MyWatchLaterFragment", "onConfigurationChanged:com.yenaly.han1meviewer.ui.fragment.home.MyWatchLaterFragment", "viewModel:com.yenaly.han1meviewer.ui.fragment.home.MyWatchLaterFragment", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.home.MyWatchLaterFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.home.MyWatchLaterFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.home.MyWatchLaterFragment", "initData:com.yenaly.han1meviewer.ui.fragment.home.MyWatchLaterFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\activity\\LoginActivity.kt": ["dismiss:com.yenaly.han1meviewer.ui.activity.LoginActivity.LoginDialog", "onCreate:com.yenaly.han1meviewer.ui.activity.LoginActivity", "onKeyDown:com.yenaly.han1meviewer.ui.activity.LoginActivity", "LoginActivity:com.yenaly.han1meviewer.ui.activity", "onDestroy:com.yenaly.han1meviewer.ui.activity.LoginActivity", "LoginDialog:com.yenaly.han1meviewer.ui.activity.LoginActivity", "onOptionsItemSelected:com.yenaly.han1meviewer.ui.activity.LoginActivity", "<init>:com.yenaly.han1meviewer.ui.activity.LoginActivity", "show:com.yenaly.han1meviewer.ui.activity.LoginActivity.LoginDialog", "setUiStyle:com.yenaly.han1meviewer.ui.activity.LoginActivity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\mylist\\WatchLaterSubViewModel.kt": ["watchLaterStateFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.WatchLaterSubViewModel", "watchLaterPage:com.yenaly.han1meviewer.ui.viewmodel.mylist.WatchLaterSubViewModel", "WatchLaterSubViewModel:com.yenaly.han1meviewer.ui.viewmodel.mylist", "deleteMyWatchLaterFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.WatchLaterSubViewModel", "deleteMyWatchLater:com.yenaly.han1meviewer.ui.viewmodel.mylist.WatchLaterSubViewModel", "watchLaterFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.WatchLaterSubViewModel", "getMyWatchLaterItems:com.yenaly.han1meviewer.ui.viewmodel.mylist.WatchLaterSubViewModel", "clearMyListItems:com.yenaly.han1meviewer.ui.viewmodel.mylist.WatchLaterSubViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\CenterLinearLayoutManager.kt": ["<init>:com.yenaly.han1meviewer.ui.view.CenterLinearLayoutManager.Companion", "Companion:com.yenaly.han1meviewer.ui.view.CenterLinearLayoutManager", "scrollHorizontallyBy:com.yenaly.han1meviewer.ui.view.CenterLinearLayoutManager", "calculateDtToFit:com.yenaly.han1meviewer.ui.view.CenterLinearLayoutManager.CenterSmoothScroller", "smoothScrollToPosition:com.yenaly.han1meviewer.ui.view.CenterLinearLayoutManager", "CenterLinearLayoutManager:com.yenaly.han1meviewer.ui.view", "scrollVerticallyBy:com.yenaly.han1meviewer.ui.view.CenterLinearLayoutManager"], "src\\main\\java\\com\\yenaly\\han1meviewer\\worker\\HUpdateWorker.kt": ["TAG:com.yenaly.han1meviewer.worker.HUpdateWorker.Companion", "NODE_ID:com.yenaly.han1meviewer.worker.HUpdateWorker.Companion", "collectOutput:com.yenaly.han1meviewer.worker.HUpdateWorker.Companion", "Companion:com.yenaly.han1meviewer.worker.HUpdateWorker", "UPDATE_APK:com.yenaly.han1meviewer.worker.HUpdateWorker.Companion", "DOWNLOAD_LINK:com.yenaly.han1meviewer.worker.HUpdateWorker.Companion", "HUpdateWorker:com.yenaly.han1meviewer.worker", "doWork:com.yenaly.han1meviewer.worker.HUpdateWorker", "enqueue:com.yenaly.han1meviewer.worker.HUpdateWorker.Companion", "<init>:com.yenaly.han1meviewer.worker.HUpdateWorker.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\mylist\\SubscriptionSubViewModel.kt": ["SubscriptionSubViewModel:com.yenaly.han1meviewer.ui.viewmodel.mylist", "subscriptionPage:com.yenaly.han1meviewer.ui.viewmodel.mylist.SubscriptionSubViewModel", "deleteSubscriptionFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.SubscriptionSubViewModel", "subscriptionFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.SubscriptionSubViewModel", "subscriptionStateFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.SubscriptionSubViewModel", "deleteSubscription:com.yenaly.han1meviewer.ui.viewmodel.mylist.SubscriptionSubViewModel", "getSubscriptions:com.yenaly.han1meviewer.ui.viewmodel.mylist.SubscriptionSubViewModel", "getSubscriptionsWithSinglePage:com.yenaly.han1meviewer.ui.viewmodel.mylist.SubscriptionSubViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\Constants.kt": ["HANIME_MAIN_HOSTNAME:com.yenaly.han1meviewer", "VIDEO_COMMENT_PREFIX:com.yenaly.han1meviewer", "FILE_PROVIDER_AUTHORITY:com.yenaly.han1meviewer", "HANIME_ALTER_BASE_URL:com.yenaly.han1meviewer", "COMMENT_TYPE:com.yenaly.han1meviewer", "CSRF_TOKEN:com.yenaly.han1meviewer", "HA1_GITHUB_FORUM_URL:com.yenaly.han1meviewer", "DATE_CODE:com.yenaly.han1meviewer", "ADVANCED_SEARCH_MAP:com.yenaly.han1meviewer", "HA1_GITHUB_RELEASES_URL:com.yenaly.han1meviewer", "COMMENT_ID:com.yenaly.han1meviewer", "FROM_VIDEO_TAG:com.yenaly.han1meviewer", "VIDEO_LAYOUT_WRAP_CONTENT:com.yenaly.han1meviewer", "SEARCH_YEAR_RANGE_START:com.yenaly.han1meviewer", "PREVIEW_COMMENT_PREFIX:com.yenaly.han1meviewer", "HA1_GITHUB_ISSUE_URL:com.yenaly.han1meviewer", "LOCAL_DATE_TIME_FORMAT:com.yenaly.han1meviewer", "SEARCH_YEAR_RANGE_END:com.yenaly.han1meviewer", "DOWNLOAD_NOTIFICATION_CHANNEL:com.yenaly.han1meviewer", "EMPTY_STRING:com.yenaly.han1meviewer", "LOCAL_DATE_FORMAT:com.yenaly.han1meviewer", "HANIME_BASE_URL:com.yenaly.han1meviewer", "LOGIN_TO_MAIN_ACTIVITY:com.yenaly.han1meviewer", "VIDEO_CODE:com.yenaly.han1meviewer", "USER_AGENT:com.yenaly.han1meviewer", "HANIME_ALTER_HOSTNAME:com.yenaly.han1meviewer", "HA1_GITHUB_API_URL:com.yenaly.han1meviewer", "VIDEO_LAYOUT_MATCH_PARENT:com.yenaly.han1meviewer", "HANIME_MAIN_BASE_URL:com.yenaly.han1meviewer", "HA1_GITHUB_URL:com.yenaly.han1meviewer", "UPDATE_NOTIFICATION_CHANNEL:com.yenaly.han1meviewer", "HANIME_LOGIN_URL:com.yenaly.han1meviewer", "LOGIN_COOKIE:com.yenaly.han1meviewer", "ALREADY_LOGIN:com.yenaly.han1meviewer"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\service\\HanimeMyListService.kt": ["createPlaylist:com.yenaly.han1meviewer.logic.network.service.HanimeMyListService", "modifyPlaylist:com.yenaly.han1meviewer.logic.network.service.HanimeMyListService", "getPlaylists:com.yenaly.han1meviewer.logic.network.service.HanimeMyListService", "getMyListItems:com.yenaly.han1meviewer.logic.network.service.HanimeMyListService", "deleteMyListItems:com.yenaly.han1meviewer.logic.network.service.HanimeMyListService", "addToMyFavVideo:com.yenaly.han1meviewer.logic.network.service.HanimeMyListService", "addToMyList:com.yenaly.han1meviewer.logic.network.service.HanimeMyListService", "HanimeMyListService:com.yenaly.han1meviewer.logic.network.service"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\home\\HomePageFragment.kt": ["onDestroyView:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment.Companion", "<init>:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment", "HomePageFragment:com.yenaly.han1meviewer.ui.fragment.home", "viewModel:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment", "initData:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment", "onConfigurationChanged:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment", "Companion:com.yenaly.han1meviewer.ui.fragment.home.HomePageFragment"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\FragmentVideoIntroductionBinding.java": ["<init>:com.yenaly.han1meviewer.databinding.FragmentVideoIntroductionBinding", "bind:com.yenaly.han1meviewer.databinding.FragmentVideoIntroductionBinding", "inflate:com.yenaly.han1meviewer.databinding.FragmentVideoIntroductionBinding", "FragmentVideoIntroductionBinding:com.yenaly.han1meviewer.databinding", "rvVideoIntro:com.yenaly.han1meviewer.databinding.FragmentVideoIntroductionBinding"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\yenaly\\han1meviewer\\logic\\dao\\SearchHistoryDao_Impl.java": ["insert:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao_Impl", "deleteByKeyword:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao_Impl", "<init>:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao_Impl", "find:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao_Impl", "getRequiredConverters:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao_Impl", "SearchHistoryDao_Impl:com.yenaly.han1meviewer.logic.dao", "delete:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao_Impl", "insertOrUpdate:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao_Impl", "loadAll:com.yenaly.han1meviewer.logic.dao.SearchHistoryDao_Impl"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\PreviewViewModel.kt": ["PreviewViewModel:com.yenaly.han1meviewer.ui.viewmodel", "getHanimePreview:com.yenaly.han1meviewer.ui.viewmodel.PreviewViewModel", "previewFlow:com.yenaly.han1meviewer.ui.viewmodel.PreviewViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\yenaly\\han1meviewer\\logic\\dao\\MiscellanyDatabase_Impl.java": ["MiscellanyDatabase_Impl:com.yenaly.han1meviewer.logic.dao", "clearAllTables:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase_Impl", "getRequiredAutoMigrationSpecs:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase_Impl", "createOpenHelper:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase_Impl", "getRequiredTypeConverters:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase_Impl", "getHKeyframeDao:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase_Impl", "getAutoMigrations:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase_Impl", "<init>:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase_Impl", "createInvalidationTracker:com.yenaly.han1meviewer.logic.dao.MiscellanyDatabase_Impl"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\state\\VideoLoadingState.kt": ["throwable:com.yenaly.han1meviewer.logic.state.VideoLoadingState.Error", "info:com.yenaly.han1meviewer.logic.state.VideoLoadingState.Success", "<init>:com.yenaly.han1meviewer.logic.state.VideoLoadingState", "<init>:com.yenaly.han1meviewer.logic.state.VideoLoadingState.NoContent", "NoContent:com.yenaly.han1meviewer.logic.state.VideoLoadingState", "Success:com.yenaly.han1meviewer.logic.state.VideoLoadingState", "Loading:com.yenaly.han1meviewer.logic.state.VideoLoadingState", "VideoLoadingState:com.yenaly.han1meviewer.logic.state", "Error:com.yenaly.han1meviewer.logic.state.VideoLoadingState", "<init>:com.yenaly.han1meviewer.logic.state.VideoLoadingState.Loading"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\exception\\CloudFlareBlockedException.kt": ["responseBody:com.yenaly.han1meviewer.logic.exception.CloudFlareBlockedException", "Companion:com.yenaly.han1meviewer.logic.exception.CloudFlareBlockedException", "<init>:com.yenaly.han1meviewer.logic.exception.CloudFlareBlockedException.Companion", "responseCode:com.yenaly.han1meviewer.logic.exception.CloudFlareBlockedException", "url:com.yenaly.han1meviewer.logic.exception.CloudFlareBlockedException", "CloudFlareBlockedException:com.yenaly.han1meviewer.logic.exception", "localizedMessages:com.yenaly.han1meviewer.logic.exception.CloudFlareBlockedException.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Files.kt": ["createDownloadName:com.yenaly.han1meviewer.util", "openDownloadedHanimeVideoLocally:com.yenaly.han1meviewer.util", "loadAssetAs:com.yenaly.han1meviewer.util", "hanimeVideoLocalFolder:com.yenaly.han1meviewer.util", "checkDownloadedHanimeFile:com.yenaly.han1meviewer.util", "DEF_VIDEO_TYPE:com.yenaly.han1meviewer.util", "getDownloadedHanimeFile:com.yenaly.han1meviewer.util", "copyTo:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\HomePage.kt": ["username:com.yenaly.han1meviewer.logic.model.HomePage", "csrfToken:com.yenaly.han1meviewer.logic.model.HomePage", "latestUpload:com.yenaly.han1meviewer.logic.model.HomePage", "videoCode:com.yenaly.han1meviewer.logic.model.HomePage.Banner", "HomePage:com.yenaly.han1meviewer.logic.model", "banner:com.yenaly.han1meviewer.logic.model.HomePage", "hanimeCurrent:com.yenaly.han1meviewer.logic.model.HomePage", "hotHanimeMonthly:com.yenaly.han1meviewer.logic.model.HomePage", "description:com.yenaly.han1meviewer.logic.model.HomePage.Banner", "title:com.yenaly.han1meviewer.logic.model.HomePage.Banner", "picUrl:com.yenaly.han1meviewer.logic.model.HomePage.Banner", "chineseSubtitle:com.yenaly.han1meviewer.logic.model.HomePage", "avatarUrl:com.yenaly.han1meviewer.logic.model.HomePage", "latestHanime:com.yenaly.han1meviewer.logic.model.HomePage", "Banner:com.yenaly.han1meviewer.logic.model.HomePage", "hanimeTheyWatched:com.yenaly.han1meviewer.logic.model.HomePage", "latestRelease:com.yenaly.han1meviewer.logic.model.HomePage"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\HDns.kt": ["Companion:com.yenaly.han1meviewer.logic.network.HDns", "HDns:com.yenaly.han1meviewer.logic.network", "<init>:com.yenaly.han1meviewer.logic.network.HDns.Companion", "<init>:com.yenaly.han1meviewer.logic.network.HDns", "lookup:com.yenaly.han1meviewer.logic.network.HDns"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\entity\\HKeyframeEntity.kt": ["group:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "itemType:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "Companion:com.yenaly.han1meviewer.logic.entity.HKeyframeType", "episode:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "keyframes:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "position:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity.Keyframe", "title:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "attached:com.yenaly.han1meviewer.logic.entity.HKeyframeHeader", "author:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "HEADER:com.yenaly.han1meviewer.logic.entity.HKeyframeType.Companion", "KeyframeTypeConverter:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "fromKeyframeList:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity.KeyframeTypeConverter", "H_KEYFRAME:com.yenaly.han1meviewer.logic.entity.HKeyframeType.Companion", "lastModifiedTime:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "createdTime:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "HKeyframeHeader:com.yenaly.han1meviewer.logic.entity", "title:com.yenaly.han1meviewer.logic.entity.HKeyframeHeader", "<init>:com.yenaly.han1meviewer.logic.entity.HKeyframeType.Companion", "HKeyframeType:com.yenaly.han1meviewer.logic.entity", "toKeyframeList:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity.KeyframeTypeConverter", "<init>:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity.KeyframeTypeConverter", "Keyframe:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "videoCode:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity", "HKeyframeEntity:com.yenaly.han1meviewer.logic.entity", "itemType:com.yenaly.han1meviewer.logic.entity.HKeyframeHeader", "prompt:com.yenaly.han1meviewer.logic.entity.HKeyframeEntity.Keyframe"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\AdapterLikeDataBindingPage.kt": ["binding:com.yenaly.han1meviewer.ui.adapter.AdapterLikeDataBindingPage", "AdapterLikeDataBindingPage:com.yenaly.han1meviewer.ui.adapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\settings\\PlayerSettingsFragment.kt": ["Companion:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment", "SHOW_BOTTOM_PROGRESS:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment.Companion", "PLAYER_SPEED:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment.Companion", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment", "SWITCH_PLAYER_KERNEL:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment.Companion", "PlayerSettingsFragment:com.yenaly.han1meviewer.ui.fragment.settings", "onPreferencesCreated:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment.Companion", "onStart:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment", "LONG_PRESS_SPEED_TIMES:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment.Companion", "SLIDE_SENSITIVITY:com.yenaly.han1meviewer.ui.fragment.settings.PlayerSettingsFragment.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\settings\\HKeyframeSettingsFragment.kt": ["H_KEYFRAME_CUSTOM_CATEGORY:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "H_KEYFRAME_MANAGE_CATEGORY:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "H_KEYFRAMES_ENABLE:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "Companion:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment", "HKeyframeSettingsFragment:com.yenaly.han1meviewer.ui.fragment.settings", "SHARED_H_KEYFRAME_MANAGE:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "onStart:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment", "SHARED_H_KEYFRAMES_ENABLE:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "H_KEYFRAME_SHARED_CATEGORY:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "SHOW_COMMENT_WHEN_COUNTDOWN:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "WHEN_COUNTDOWN_REMIND:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "onPreferencesCreated:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment", "H_KEYFRAME_MANAGE:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "SHARED_H_KEYFRAMES_USE_FIRST:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment.Companion", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.HKeyframeSettingsFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\home\\MyPlaylistFragment.kt": ["viewModel:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "initData:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "setupToolbar:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "bindDataObservers:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "onConfigurationChanged:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "MyPlaylistFragment:com.yenaly.han1meviewer.ui.fragment.home", "onStart:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "listTitle:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "listCode:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "getNewPlaylistItems:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.home.MyPlaylistFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\HanimeDownloadingRvAdapter.kt": ["Companion:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadingRvAdapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadingRvAdapter", "HanimeDownloadingRvAdapter:com.yenaly.han1meviewer.ui.adapter", "cancelUniqueWorkAndPause:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadingRvAdapter", "continueWork:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadingRvAdapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadingRvAdapter", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadingRvAdapter.Companion", "<init>:com.yenaly.han1meviewer.ui.adapter.HanimeDownloadingRvAdapter.Companion"], "build\\generated\\source\\buildConfig\\debug\\com\\yenaly\\han1meviewer\\BuildConfig.java": ["BuildConfig:com.yenaly.han1meviewer", "DEBUG:com.yenaly.han1meviewer.BuildConfig", "VERSION_NAME:com.yenaly.han1meviewer.BuildConfig", "COMMIT_SHA:com.yenaly.han1meviewer.BuildConfig", "SEARCH_YEAR_RANGE_END:com.yenaly.han1meviewer.BuildConfig", "<init>:com.yenaly.han1meviewer.BuildConfig", "HA1_VERSION_SOURCE:com.yenaly.han1meviewer.BuildConfig", "HA1_GITHUB_TOKEN:com.yenaly.han1meviewer.BuildConfig", "APPLICATION_ID:com.yenaly.han1meviewer.BuildConfig", "BUILD_TYPE:com.yenaly.han1meviewer.BuildConfig", "VERSION_CODE:com.yenaly.han1meviewer.BuildConfig"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Animations.kt": ["colorTransition:com.yenaly.han1meviewer.util", "addUpdateListener:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\HorizontalNestedScrollView.kt": ["dispatchTouchEvent:com.yenaly.han1meviewer.ui.view.HorizontalNestedScrollView", "requestDisallowInterceptTouchEvent:com.yenaly.han1meviewer.ui.view.HorizontalNestedScrollView", "HorizontalNestedScrollView:com.yenaly.han1meviewer.ui.view"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\video\\GSYMediaKernel.kt": ["onSurfaceTextureSizeChanged:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "onSurfaceTextureUpdated:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "<init>:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel.OnBufferingUpdate", "start:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "setSurface:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "<init>:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel.Companion", "Companion:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "isPlaying:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "prepare:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "pause:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "seekTo:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "setVolume:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "TAG:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel.Companion", "onSurfaceTextureDestroyed:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "getCurrentPosition:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "setSpeed:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "release:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "onSurfaceTextureAvailable:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "GSYMediaKernel:com.yenaly.han1meviewer.ui.view.video", "getDuration:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel", "run:com.yenaly.han1meviewer.ui.view.video.GSYMediaKernel.OnBufferingUpdate"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\FixedGridLayoutManager.kt": ["FixedGridLayoutManager:com.yenaly.han1meviewer.ui.adapter", "supportsPredictiveItemAnimations:com.yenaly.han1meviewer.ui.adapter.FixedGridLayoutManager"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\network\\HProxySelector.kt": ["rebuildNetwork:com.yenaly.han1meviewer.logic.network.HProxySelector.Companion", "Companion:com.yenaly.han1meviewer.logic.network.HProxySelector", "TYPE_DIRECT:com.yenaly.han1meviewer.logic.network.HProxySelector.Companion", "TYPE_SYSTEM:com.yenaly.han1meviewer.logic.network.HProxySelector.Companion", "<init>:com.yenaly.han1meviewer.logic.network.HProxySelector.Companion", "select:com.yenaly.han1meviewer.logic.network.HProxySelector", "<init>:com.yenaly.han1meviewer.logic.network.HProxySelector", "validatePort:com.yenaly.han1meviewer.logic.network.HProxySelector.Companion", "TYPE_SOCKS:com.yenaly.han1meviewer.logic.network.HProxySelector.Companion", "validateIp:com.yenaly.han1meviewer.logic.network.HProxySelector.Companion", "connectFailed:com.yenaly.han1meviewer.logic.network.HProxySelector", "HProxySelector:com.yenaly.han1meviewer.logic.network", "TYPE_HTTP:com.yenaly.han1meviewer.logic.network.HProxySelector.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\IHCsrfToken.kt": ["csrfToken:com.yenaly.han1meviewer.ui.viewmodel.IHCsrfToken", "IHCsrfToken:com.yenaly.han1meviewer.ui.viewmodel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\SharedHKeyframesRvAdapter.kt": ["<init>:com.yenaly.han1meviewer.ui.adapter.SharedHKeyframesRvAdapter", "Companion:com.yenaly.han1meviewer.ui.adapter.SharedHKeyframesRvAdapter", "onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.SharedHKeyframesRvAdapter", "SharedHKeyframesRvAdapter:com.yenaly.han1meviewer.ui.adapter", "getItemViewType:com.yenaly.han1meviewer.ui.adapter.SharedHKeyframesRvAdapter", "<init>:com.yenaly.han1meviewer.ui.adapter.SharedHKeyframesRvAdapter.Companion", "COMPARATOR:com.yenaly.han1meviewer.ui.adapter.SharedHKeyframesRvAdapter.Companion", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.SharedHKeyframesRvAdapter"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\search\\HCheckBoxFragment.kt": ["scopeNameRes:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment", "newInstance:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment.Companion", "HCheckBoxFragment:com.yenaly.han1meviewer.ui.fragment.search", "Companion:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment", "ITEMS:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment.Companion", "<init>:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment.Companion", "DEF_SPAN_COUNT:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment.Companion", "items:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment", "onCreateView:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment", "SCOPE_NAME_RES:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment.Companion", "spanCount:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment", "onViewCreated:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment", "adapter:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment", "SPAN_COUNT:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment.Companion", "<init>:com.yenaly.han1meviewer.ui.fragment.search.HCheckBoxFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\LinearSmoothToStartScroller.kt": ["getHorizontalSnapPreference:com.yenaly.han1meviewer.ui.view.LinearSmoothToStartScroller", "getVerticalSnapPreference:com.yenaly.han1meviewer.ui.view.LinearSmoothToStartScroller", "LinearSmoothToStartScroller:com.yenaly.han1meviewer.ui.view"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\activity\\CloudflareBypassActivity.kt": ["CloudflareBypassActivity:com.yenaly.han1meviewer.ui.activity", "<init>:com.yenaly.han1meviewer.ui.activity.CloudflareBypassActivity", "setUiStyle:com.yenaly.han1meviewer.ui.activity.CloudflareBypassActivity", "onOptionsItemSelected:com.yenaly.han1meviewer.ui.activity.CloudflareBypassActivity", "<init>:com.yenaly.han1meviewer.ui.activity.CloudflareBypassActivity.Companion", "onDestroy:com.yenaly.han1meviewer.ui.activity.CloudflareBypassActivity", "Companion:com.yenaly.han1meviewer.ui.activity.CloudflareBypassActivity", "onCreate:com.yenaly.han1meviewer.ui.activity.CloudflareBypassActivity", "start:com.yenaly.han1meviewer.ui.activity.CloudflareBypassActivity.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\viewmodel\\mylist\\PlaylistSubViewModel.kt": ["playlistDesc:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "playlistPage:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "getPlaylistItems:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "createPlaylist:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "modifyPlaylist:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "modifyPlaylistFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "playlistFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "clearMyListItems:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "deleteFromPlaylistFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "playlistStateFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "PlaylistSubViewModel:com.yenaly.han1meviewer.ui.viewmodel.mylist", "playlistTitle:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "deleteFromPlaylist:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "getPlaylists:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "playlistCode:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "createPlaylistFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel", "playlistsFlow:com.yenaly.han1meviewer.ui.viewmodel.mylist.PlaylistSubViewModel"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\model\\github\\Workflow.kt": ["WorkflowRun:com.yenaly.han1meviewer.logic.model.github.WorkflowRuns", "downloadLink:com.yenaly.han1meviewer.logic.model.github.Artifacts", "artifacts:com.yenaly.han1meviewer.logic.model.github.Artifacts", "WorkflowRuns:com.yenaly.han1meviewer.logic.model.github", "headSha:com.yenaly.han1meviewer.logic.model.github.WorkflowRuns.WorkflowRun", "workflowRuns:com.yenaly.han1meviewer.logic.model.github.WorkflowRuns", "nodeId:com.yenaly.han1meviewer.logic.model.github.Artifacts.Artifact", "nodeId:com.yenaly.han1meviewer.logic.model.github.Artifacts", "name:com.yenaly.han1meviewer.logic.model.github.Artifacts.Artifact", "artifactsUrl:com.yenaly.han1meviewer.logic.model.github.WorkflowRuns.WorkflowRun", "Artifacts:com.yenaly.han1meviewer.logic.model.github", "Artifact:com.yenaly.han1meviewer.logic.model.github.Artifacts", "downloadLink:com.yenaly.han1meviewer.logic.model.github.Artifacts.Artifact", "title:com.yenaly.han1meviewer.logic.model.github.WorkflowRuns.WorkflowRun"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\pref\\MaterialDialogPreference.kt": ["onClick:com.yenaly.han1meviewer.ui.view.pref.MaterialDialogPreference", "MaterialDialogPreference:com.yenaly.han1meviewer.ui.view.pref"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Permissions.kt": ["pickVisualMedia:com.yenaly.han1meviewer.util", "requestPostNotificationPermission:com.yenaly.han1meviewer.util", "requestInstallPermission:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\exception\\HanimeNotFoundException.kt": ["HanimeNotFoundException:com.yenaly.han1meviewer.logic.exception"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\activity\\SearchActivity.kt": ["searchText:com.yenaly.han1meviewer.ui.activity.SearchActivity", "onConfigurationChanged:com.yenaly.han1meviewer.ui.activity.SearchActivity", "SearchActivity:com.yenaly.han1meviewer.ui.activity", "initData:com.yenaly.han1meviewer.ui.activity.SearchActivity", "myListViewModel:com.yenaly.han1meviewer.ui.activity.SearchActivity", "getViewBinding:com.yenaly.han1meviewer.ui.activity.SearchActivity", "subscribeLauncher:com.yenaly.han1meviewer.ui.activity.SearchActivity", "setSearchText:com.yenaly.han1meviewer.ui.activity.SearchActivity", "setUiStyle:com.yenaly.han1meviewer.ui.activity.SearchActivity", "onFragmentResumedListener:com.yenaly.han1meviewer.ui.activity.SearchActivity", "bindDataObservers:com.yenaly.han1meviewer.ui.activity.SearchActivity", "viewModel:com.yenaly.han1meviewer.ui.activity.SearchActivity", "<init>:com.yenaly.han1meviewer.ui.activity.SearchActivity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Firebases.kt": ["logScreenViewEvent:com.yenaly.han1meviewer.util"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\FragmentListOnlyBinding.java": ["FragmentListOnlyBinding:com.yenaly.han1meviewer.databinding", "bind:com.yenaly.han1meviewer.databinding.FragmentListOnlyBinding", "inflate:com.yenaly.han1meviewer.databinding.FragmentListOnlyBinding", "rvList:com.yenaly.han1meviewer.databinding.FragmentListOnlyBinding", "<init>:com.yenaly.han1meviewer.databinding.FragmentListOnlyBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\settings\\HomeSettingsFragment.kt": ["setupToolbar:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment", "Companion:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment", "SUBMIT_BUG:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "FORUM:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "<init>:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "VIDEO_LANGUAGE:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "onViewCreated:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment", "UPDATE_POPUP_INTERVAL_DAYS:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "CLEAR_CACHE:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "USE_ANALYTICS:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "ABOUT:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "DOWNLOAD_PATH:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "LAST_UPDATE_POPUP_TIME:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "onStart:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment", "HomeSettingsFragment:com.yenaly.han1meviewer.ui.fragment.settings", "NETWORK_SETTINGS:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "USE_CI_UPDATE_CHANNEL:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "PLAYER_SETTINGS:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "H_KEYFRAME_SETTINGS:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "APPLY_DEEP_LINKS:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion", "onPreferencesCreated:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment", "UPDATE:com.yenaly.han1meviewer.ui.fragment.settings.HomeSettingsFragment.Companion"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\view\\video\\HanimeDataSource.kt": ["getValueFromLinkedMap:com.yenaly.han1meviewer.ui.view.video.HanimeDataSource", "HanimeDataSource:com.yenaly.han1meviewer.ui.view.video", "getKeyFromDataSource:com.yenaly.han1meviewer.ui.view.video.HanimeDataSource"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\fragment\\video\\ChildCommentPopupFragment.kt": ["<init>:com.yenaly.han1meviewer.ui.fragment.video.ChildCommentPopupFragment", "ChildCommentPopupFragment:com.yenaly.han1meviewer.ui.fragment.video", "commentId:com.yenaly.han1meviewer.ui.fragment.video.ChildCommentPopupFragment", "viewModel:com.yenaly.han1meviewer.ui.fragment.video.ChildCommentPopupFragment", "initData:com.yenaly.han1meviewer.ui.fragment.video.ChildCommentPopupFragment", "getViewBinding:com.yenaly.han1meviewer.ui.fragment.video.ChildCommentPopupFragment"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\popup\\CoilImageLoader.kt": ["loadImage:com.yenaly.han1meviewer.ui.popup.CoilImageLoader", "getImageFile:com.yenaly.han1meviewer.ui.popup.CoilImageLoader", "loadSnapshot:com.yenaly.han1meviewer.ui.popup.CoilImageLoader", "CoilImageLoader:com.yenaly.han1meviewer.ui.popup"], "build\\generated\\data_binding_base_class_source_out\\debug\\out\\com\\yenaly\\han1meviewer\\databinding\\ActivityVideoBinding.java": ["ActivityVideoBinding:com.yenaly.han1meviewer.databinding", "bind:com.yenaly.han1meviewer.databinding.ActivityVideoBinding", "videoVp:com.yenaly.han1meviewer.databinding.ActivityVideoBinding", "videoPlayer:com.yenaly.han1meviewer.databinding.ActivityVideoBinding", "videoTl:com.yenaly.han1meviewer.databinding.ActivityVideoBinding", "<init>:com.yenaly.han1meviewer.databinding.ActivityVideoBinding", "inflate:com.yenaly.han1meviewer.databinding.ActivityVideoBinding"], "src\\main\\java\\com\\yenaly\\han1meviewer\\logic\\entity\\HanimeDownloadEntity.kt": ["progress:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "videoCode:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "TITLE:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity.SortedBy", "coverUrl:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "ID:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity.SortedBy", "id:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "videoUrl:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "<init>:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity.SortedBy.TITLE", "downloadedLength:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "addDate:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "videoUri:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "quality:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "HanimeDownloadEntity:com.yenaly.han1meviewer.logic.entity", "title:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "SortedBy:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "<init>:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity.SortedBy.ID", "length:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "isDownloading:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity", "isDownloaded:com.yenaly.han1meviewer.logic.entity.HanimeDownloadEntity"], "src\\main\\java\\com\\yenaly\\han1meviewer\\util\\Networks.kt": ["<init>:com.yenaly.han1meviewer.util.DirectExecutor", "runSuspendCatching:com.yenaly.han1meviewer.util", "execute:com.yenaly.han1meviewer.util.DirectExecutor", "await:com.yenaly.han1meviewer.util"], "src\\main\\java\\com\\yenaly\\han1meviewer\\ui\\adapter\\PlaylistRvAdapter.kt": ["onCreateViewHolder:com.yenaly.han1meviewer.ui.adapter.PlaylistRvAdapter", "PlaylistRvAdapter:com.yenaly.han1meviewer.ui.adapter", "onBindViewHolder:com.yenaly.han1meviewer.ui.adapter.PlaylistRvAdapter"]}